package com.anytech.anytxn.authorization.service.auth.visa;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.service.channel.visa.VisaAuthDetailDataModifyServiceImpl;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingResDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @description Unit test class for VisaLogAuthProcessUpdDataServiceImpl
 * <AUTHOR>
 * @date 2025/07/09
 * @version 1.0
 * @AI Reviewed
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Visa Log Authorization Process Update Data Service Test")
class VisaLogAuthProcessUpdDataServiceImplTest {

    @Mock
    private VisaAuthDetailDataModifyServiceImpl visaAuthDetailDataModifyService;

    @InjectMocks
    private VisaLogAuthProcessUpdDataServiceImpl visaLogAuthProcessUpdDataService;

    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;
    private AuthRecordedDTO authRecordedDTO;
    private AccountManagementInfoDTO accountManagementInfoDTO;
    private OrganizationInfoResDTO orgInfoDTO;
    private AuthorisationProcessingResDTO authorProcessInfoDTO;
    
    private MockedStatic<OrgNumberUtils> mockedOrgNumberUtils;

    @BeforeEach
    void setUp() {
        // Mock static methods
        mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class);
        mockedOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("123456");
        
        // Initialize test data
        authorizationCheckProcessingPayload = new AuthorizationCheckProcessingPayload();
        authRecordedDTO = new AuthRecordedDTO();
        accountManagementInfoDTO = new AccountManagementInfoDTO();
        orgInfoDTO = new OrganizationInfoResDTO();
        authorProcessInfoDTO = new AuthorisationProcessingResDTO();

        // Set basic data
        authRecordedDTO.setAuthGlobalFlowNumber("TEST123456");
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("1000.00"));
        authRecordedDTO.setAuthCardNumber("****************");
        authRecordedDTO.setAuthAuthCode("123456");

        authorizationCheckProcessingPayload.setAuthRecordedDTO(authRecordedDTO);
        authorizationCheckProcessingPayload.setAccountManagementInfoDTO(accountManagementInfoDTO);
        authorizationCheckProcessingPayload.setOrgInfo(orgInfoDTO);
        authorizationCheckProcessingPayload.setAuthorProcessInfo(authorProcessInfoDTO);
    }

    @AfterEach
    void tearDown() {
        // Close static mock
        if (mockedOrgNumberUtils != null) {
            mockedOrgNumberUtils.close();
        }
    }

    @Test
    @DisplayName("Test updateData method - Normal authorization log addition")
    void testUpdateData_NormalAuthorizationLogAddition() {
        // Arrange
        int expectedResult = 1;
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(expectedResult);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(expectedResult, result);
        
        // Verify service interaction
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - Authorization log addition returns zero")
    void testUpdateData_AuthorizationLogAdditionReturnsZero() {
        // Arrange
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(0);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(0, result);
        
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - Authorization log addition returns negative value")
    void testUpdateData_AuthorizationLogAdditionReturnsNegative() {
        // Arrange
        int expectedResult = -1;
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(expectedResult);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(expectedResult, result);
        
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - Large positive return value")
    void testUpdateData_LargePositiveReturnValue() {
        // Arrange
        int expectedResult = 999999;
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(expectedResult);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(expectedResult, result);
        
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - VisaAuthDetailDataModifyService throws exception")
    void testUpdateData_VisaAuthDetailDataModifyServiceException() {
        // Arrange
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenThrow(new RuntimeException("Database connection error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        });
        
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - VisaAuthDetailDataModifyService throws SQL exception")
    void testUpdateData_VisaAuthDetailDataModifyServiceSQLException() {
        // Arrange
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenThrow(new RuntimeException("SQL constraint violation"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        });
        
        assertEquals("SQL constraint violation", exception.getMessage());
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - Null payload parameter")
    void testUpdateData_NullPayload() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            visaLogAuthProcessUpdDataService.updateData(null);
        });
        
        // Verify no service interactions
        verifyNoInteractions(visaAuthDetailDataModifyService);
    }

    @Test
    @DisplayName("Test updateData method - Null authRecordedDTO")
    void testUpdateData_NullAuthRecordedDTO() {
        // Arrange
        authorizationCheckProcessingPayload.setAuthRecordedDTO(null);
        when(visaAuthDetailDataModifyService.addAuthorizationLog(null))
            .thenReturn(0);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(0, result);

        // Verify service is called with null parameter
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(null);
    }

    @Test
    @DisplayName("Test updateData method - AuthRecordedDTO with minimal data")
    void testUpdateData_AuthRecordedDTOWithMinimalData() {
        // Arrange
        AuthRecordedDTO minimalAuthRecordedDTO = new AuthRecordedDTO();
        minimalAuthRecordedDTO.setAuthGlobalFlowNumber("MIN123");
        authorizationCheckProcessingPayload.setAuthRecordedDTO(minimalAuthRecordedDTO);
        
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(1);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(1, result);
        
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(minimalAuthRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - AuthRecordedDTO with large transaction amount")
    void testUpdateData_AuthRecordedDTOWithLargeAmount() {
        // Arrange
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("999999999.99"));
        
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(1);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(1, result);
        
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - AuthRecordedDTO with zero transaction amount")
    void testUpdateData_AuthRecordedDTOWithZeroAmount() {
        // Arrange
        authRecordedDTO.setAuthTransactionAmount(BigDecimal.ZERO);
        
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(1);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(1, result);
        
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - AuthRecordedDTO with negative transaction amount")
    void testUpdateData_AuthRecordedDTOWithNegativeAmount() {
        // Arrange
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("-100.00"));
        
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(1);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(1, result);
        
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - Multiple consecutive calls")
    void testUpdateData_MultipleConsecutiveCalls() {
        // Arrange
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(1, 2, 3); // Different return values for each call

        // Act
        int firstResult = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        int secondResult = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        int thirdResult = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(1, firstResult);
        assertEquals(2, secondResult);
        assertEquals(3, thirdResult);
        
        verify(visaAuthDetailDataModifyService, times(3))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - Verify method delegation")
    void testUpdateData_VerifyMethodDelegation() {
        // Arrange
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(42);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(42, result);
        
        // Verify that the method is a simple delegation
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
        verifyNoMoreInteractions(visaAuthDetailDataModifyService);
    }

    @Test
    @DisplayName("Test updateData method - AuthRecordedDTO with special characters in card number")
    void testUpdateData_AuthRecordedDTOWithSpecialCharacters() {
        // Arrange
        authRecordedDTO.setAuthCardNumber("4111-1111-1111-1111");
        authRecordedDTO.setAuthAuthCode("ABC123");
        
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(1);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(1, result);
        
        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("Test updateData method - AuthRecordedDTO with empty strings")
    void testUpdateData_AuthRecordedDTOWithEmptyStrings() {
        // Arrange
        authRecordedDTO.setAuthCardNumber("");
        authRecordedDTO.setAuthAuthCode("");
        authRecordedDTO.setAuthGlobalFlowNumber("");

        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(1);

        // Act
        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(1, result);

        verify(visaAuthDetailDataModifyService, times(1))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("测试updateData方法-服务依赖注入验证")
    void testUpdateData_ServiceDependencyInjection() {
        // 验证依赖注入是否正确
        assertNotNull(visaAuthDetailDataModifyService);
        assertNotNull(visaLogAuthProcessUpdDataService);

        // 测试服务调用
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(1);

        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        assertEquals(1, result);
    }

    @Test
    @DisplayName("测试updateData方法-边界值测试")
    void testUpdateData_BoundaryValues() {
        // 测试Integer边界值
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(Integer.MAX_VALUE, Integer.MIN_VALUE);

        // Act & Assert
        assertEquals(Integer.MAX_VALUE, visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload));
        assertEquals(Integer.MIN_VALUE, visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload));

        verify(visaAuthDetailDataModifyService, times(2)).addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("测试updateData方法-方法签名验证")
    void testUpdateData_MethodSignature() throws NoSuchMethodException {
        // 验证方法签名
        java.lang.reflect.Method method = VisaLogAuthProcessUpdDataServiceImpl.class
                .getDeclaredMethod("updateData", AuthorizationCheckProcessingPayload.class);

        assertNotNull(method);
        assertEquals("updateData", method.getName());
        assertEquals(int.class, method.getReturnType());
        assertEquals(1, method.getParameterCount());
    }

    @Test
    @DisplayName("测试updateData方法-类型验证")
    void testUpdateData_ClassType() {
        // 验证类类型
        assertFalse(VisaLogAuthProcessUpdDataServiceImpl.class.isInterface());
        assertTrue(VisaLogAuthProcessUpdDataServiceImpl.class.isAnnotationPresent(org.springframework.stereotype.Component.class));
        assertEquals("VisaLogAuthProcessUpdDataServiceImpl", VisaLogAuthProcessUpdDataServiceImpl.class.getSimpleName());
    }

    @Test
    @DisplayName("测试updateData方法-异常处理增强")
    void testUpdateData_ExceptionHandlingEnhanced() {
        // 测试各种异常类型
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenThrow(new IllegalArgumentException("Invalid argument"))
            .thenThrow(new IllegalStateException("Invalid state"))
            .thenThrow(new UnsupportedOperationException("Unsupported operation"));

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () ->
            visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload));
        assertThrows(IllegalStateException.class, () ->
            visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload));
        assertThrows(UnsupportedOperationException.class, () ->
            visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload));

        verify(visaAuthDetailDataModifyService, times(3)).addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("测试updateData方法-参数传递验证")
    void testUpdateData_ParameterPassing() {
        // 验证参数正确传递
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenAnswer(invocation -> {
                AuthRecordedDTO dto = invocation.getArgument(0);
                assertSame(authRecordedDTO, dto);
                return 1;
            });

        int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        assertEquals(1, result);
        verify(visaAuthDetailDataModifyService).addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("测试updateData方法-返回值直接传递验证")
    void testUpdateData_ReturnValueDirectPass() {
        // 验证返回值直接传递，没有额外处理
        int[] testValues = {0, 1, -1, 100, -100, 999, -999};

        for (int testValue : testValues) {
            when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
                .thenReturn(testValue);

            int result = visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
            assertEquals(testValue, result, "返回值应该直接传递，不做任何修改");
        }

        verify(visaAuthDetailDataModifyService, times(testValues.length))
            .addAuthorizationLog(eq(authRecordedDTO));
    }

    @Test
    @DisplayName("测试updateData方法-Mock验证增强")
    void testUpdateData_MockVerificationEnhanced() {
        // 设置Mock行为
        when(visaAuthDetailDataModifyService.addAuthorizationLog(any(AuthRecordedDTO.class)))
            .thenReturn(1);

        // 执行测试
        visaLogAuthProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // 详细验证Mock交互
        verify(visaAuthDetailDataModifyService, times(1)).addAuthorizationLog(authRecordedDTO);
        verify(visaAuthDetailDataModifyService, never()).addAuthorizationLog(null);
        verify(visaAuthDetailDataModifyService, atLeastOnce()).addAuthorizationLog(any(AuthRecordedDTO.class));
        verify(visaAuthDetailDataModifyService, atMost(1)).addAuthorizationLog(any(AuthRecordedDTO.class));
        verifyNoMoreInteractions(visaAuthDetailDataModifyService);
    }
}