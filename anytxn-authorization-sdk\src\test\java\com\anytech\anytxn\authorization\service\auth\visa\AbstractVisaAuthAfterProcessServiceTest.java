/**
 * @description 抽象Visa授权后处理服务测试类
 * <AUTHOR>
 * @date 2025/07/09
 * @version 1.0
 * @AI 已复核
 */
package com.anytech.anytxn.authorization.service.auth.visa;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.service.auth.AuthProcessUpdDataService;
import com.anytech.anytxn.authorization.service.channel.visa.VisaAuthDetailDataModifyServiceImpl;
import com.anytech.anytxn.authorization.service.channel.visa.VisaCheckAuthResponseCodeServiceImpl;
import com.anytech.anytxn.authorization.service.manager.VisaAuthDataUpdateManager;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTrancactionStatusEnum;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatisticsInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCurrencyCodeSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("抽象Visa授权后处理服务测试")
class AbstractVisaAuthAfterProcessServiceTest {

    @Mock
    private VisaAuthDetailDataModifyServiceImpl visaAuthDetailDataModifyService;

    @Mock
    private VisaCheckAuthResponseCodeServiceImpl visaCheckAuthResponseCodeServiceImpl;

    @Mock
    private VisaAuthDataUpdateManager visaAuthDataUpdateManager;

    @Mock
    private AccountStatisticsInfoSelfMapper accountStatisticsInfoSelfMapper;

    @Mock
    private ParmCurrencyCodeSelfMapper parmCurrencyCodeSelfMapper;

    @Mock
    private ParmCurrencyRateSelfMapper parmCurrencyRateSelfMapper;

    @Mock
    private AuthProcessUpdDataService authProcessUpdDataService;

    @InjectMocks
    private ConcreteVisaAuthAfterProcessService concreteService;

    private AuthorizationCheckProcessingPayload payload;
    private AuthRecordedDTO authRecordedDTO;
    private AccountManagementInfoDTO accountManagementInfoDTO;

    // 具体实现类用于测试抽象类
    private static class ConcreteVisaAuthAfterProcessService extends AbstractVisaAuthAfterProcessService {
        // 具体实现类，用于测试抽象类
    }

    @BeforeEach
    void setUp() {
        // 使用MockedStatic来初始化测试数据，因为AuthRecordedDTO构造函数会调用OrgNumberUtils
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            // 初始化测试数据
            payload = new AuthorizationCheckProcessingPayload();
            authRecordedDTO = new AuthRecordedDTO();
            accountManagementInfoDTO = new AccountManagementInfoDTO();

            authRecordedDTO.setAuthGlobalFlowNumber("12345678901234567890");
            authRecordedDTO.setAuthProcessingCode("000000");
            authRecordedDTO.setAuthBillingCurrencyCode("702");
            authRecordedDTO.setOrganizationNumber("123456");
            authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());

            accountManagementInfoDTO.setAccountManagementId("ACC123456");

            payload.setAuthRecordedDTO(authRecordedDTO);
            payload.setAccountManagementInfoDTO(accountManagementInfoDTO);

            // 设置依赖服务
            concreteService.setAuthProcessUpdDataService(authProcessUpdDataService);
        }
    }

    /**
     * 执行带有静态Mock的测试
     */
    private void executeWithStaticMocks(Runnable testLogic) {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            testLogic.run();
        }
    }

    @Test
    @DisplayName("测试setResF054Value - 正常余额")
    void testSetResF054Value_WithPositiveBalance() {
        executeWithStaticMocks(() -> {
            // Given
            AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
            accountStatisticsInfo.setBalance(new BigDecimal("1000.50"));

            lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                    .thenReturn(accountStatisticsInfo);

            // When
            concreteService.setResF054Value(payload);

            // Then
            assertNotNull(authRecordedDTO.getAuthAdditionalAmounts());
            assertTrue(authRecordedDTO.getAuthAdditionalAmounts().length() > 0);
        });
    }

    @Test
    @DisplayName("测试setResF054Value - 账户信息为空")
    void testSetResF054Value_WithNullAccountInfo() {
        // Given
        lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // When
        concreteService.setResF054Value(payload);

        // Then
        assertNotNull(authRecordedDTO.getAuthAdditionalAmounts());
        assertTrue(authRecordedDTO.getAuthAdditionalAmounts().length() > 0);
    }

    @Test
    @DisplayName("测试execute - 授权检查通过")
    void testExecute_AuthorizationSuccess() {
        // Given
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());

        // When
        concreteService.execute(payload);

        // Then
        verify(authProcessUpdDataService).updateData(payload);
        assertEquals(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
    }

    @Test
    @DisplayName("测试execute - 部分批准")
    void testExecute_PartialApproval() {
        // Given
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.PARTIAL_APPROVAL.getCode());

        // When
        concreteService.execute(payload);

        // Then
        verify(authProcessUpdDataService).updateData(payload);
        assertEquals(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
    }

    @Test
    @DisplayName("测试execute - 授权检查失败")
    void testExecute_AuthorizationFailed() {
        // Given
        authRecordedDTO.setAuthResponseCode("05"); // 拒绝码
        authRecordedDTO.setErrorDetailCode("ERROR001");

        // When
        concreteService.execute(payload);

        // Then
        verify(visaAuthDataUpdateManager).updateAuthorization(payload);
        verify(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicC(payload);
        assertEquals(AuthTrancactionStatusEnum.ERROR_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
        verify(authProcessUpdDataService, never()).updateData(payload);
    }

    @Test
    @DisplayName("测试anticipatedAmountVerification - 信息为空")
    void testAnticipatedAmountVerification_EmptyAnticipatedInfo() {
        // Given
        authRecordedDTO.setAuthAdditionalAmounts("");

        // When
        concreteService.anticipatedAmountVerification(payload);

        // Then
        assertEquals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(), authRecordedDTO.getAuthResponseCode());
    }

    @Test
    @DisplayName("测试anticipatedAmountVerification - 信息长度不正确")
    void testAnticipatedAmountVerification_IncorrectLength() {
        // Given
        authRecordedDTO.setAuthAdditionalAmounts("00447020"); // 长度不足30

        // When
        concreteService.anticipatedAmountVerification(payload);

        // Then
        assertEquals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(), authRecordedDTO.getAuthResponseCode());
    }

    @Test
    @DisplayName("测试anticipatedAmountVerification - 类型码不匹配")
    void testAnticipatedAmountVerification_IncorrectTypeCode() {
        // Given
        authRecordedDTO.setAuthAdditionalAmounts("004370200000000000100000"); // 类型码43而非44

        // When
        concreteService.anticipatedAmountVerification(payload);

        // Then
        assertEquals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(), authRecordedDTO.getAuthResponseCode());
    }

    @Test
    @DisplayName("测试anticipatedAmountVerification - 金额充足")
    void testAnticipatedAmountVerification_SufficientAmount() {
        executeWithStaticMocks(() -> {
            // Given
            authRecordedDTO.setAuthAdditionalAmounts("004470200000000000100000");
            AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
            accountStatisticsInfo.setBalance(new BigDecimal("2000.00"));

            lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                    .thenReturn(accountStatisticsInfo);

            // When
            concreteService.anticipatedAmountVerification(payload);

            // Then
            assertEquals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(), authRecordedDTO.getAuthResponseCode());
        });
    }

    @Test
    @DisplayName("测试anticipatedAmountVerification - 金额不足")
    void testAnticipatedAmountVerification_InsufficientAmount() {
        executeWithStaticMocks(() -> {
            // Given
            authRecordedDTO.setAuthAdditionalAmounts("004470200000000000500000");
            AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
            accountStatisticsInfo.setBalance(new BigDecimal("100.00"));

            lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                    .thenReturn(accountStatisticsInfo);

            // When
            concreteService.anticipatedAmountVerification(payload);

            // Then
            assertEquals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(), authRecordedDTO.getAuthResponseCode());
        });
    }

    @Test
    @DisplayName("测试anticipatedAmountVerification - 不同货币代码转换")
    void testAnticipatedAmountVerification_DifferentCurrency() {
        executeWithStaticMocks(() -> {
            // Given
            authRecordedDTO.setAuthAdditionalAmounts("004484************100000");
            authRecordedDTO.setAuthBillingCurrencyCode("702");
            AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
            accountStatisticsInfo.setBalance(new BigDecimal("1000.00"));

            lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                    .thenReturn(accountStatisticsInfo);

            // When
            concreteService.anticipatedAmountVerification(payload);

            // Then
            assertEquals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(), authRecordedDTO.getAuthResponseCode());
        });
    }

    @Test
    @DisplayName("测试setResF054Value - 负余额")
    void testSetResF054Value_WithNegativeBalance() {
        executeWithStaticMocks(() -> {
            // Given
            AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
            accountStatisticsInfo.setBalance(new BigDecimal("-500.50"));

            lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                    .thenReturn(accountStatisticsInfo);

            // When
            concreteService.setResF054Value(payload);

            // Then
            assertNotNull(authRecordedDTO.getAuthAdditionalAmounts());
            assertTrue(authRecordedDTO.getAuthAdditionalAmounts().contains("D")); // 负数标识
        });
    }

    @Test
    @DisplayName("测试setResF054Value - 零余额")
    void testSetResF054Value_WithZeroBalance() {
        executeWithStaticMocks(() -> {
            // Given
            AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
            accountStatisticsInfo.setBalance(BigDecimal.ZERO);

            lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                    .thenReturn(accountStatisticsInfo);

            // When
            concreteService.setResF054Value(payload);

            // Then
            assertNotNull(authRecordedDTO.getAuthAdditionalAmounts());
            assertTrue(authRecordedDTO.getAuthAdditionalAmounts().contains("D")); // 零被视为负数
        });
    }

    @Test
    @DisplayName("测试execute - 授权日志标识为是")
    void testExecute_WithAuthorizationLogFlag() {
        executeWithStaticMocks(() -> {
            // Given
            authRecordedDTO.setAuthResponseCode("05"); // 拒绝码
            SystemTableDTO systemInfo = new SystemTableDTO();
            systemInfo.setAuthorizationLogFlag("1"); // 使用正确的值
            payload.setSystemInfo(systemInfo);

            // When
            concreteService.execute(payload);

            // Then
            verify(visaAuthDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            verify(visaAuthDataUpdateManager).updateAuthorization(payload);
            verify(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicC(payload);
            assertEquals(AuthTrancactionStatusEnum.ERROR_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
        });
    }

    @Test
    @DisplayName("测试execute - 授权日志标识为否")
    void testExecute_WithoutAuthorizationLogFlag() {
        executeWithStaticMocks(() -> {
            // Given
            authRecordedDTO.setAuthResponseCode("05"); // 拒绝码
            SystemTableDTO systemInfo = new SystemTableDTO();
            systemInfo.setAuthorizationLogFlag("0"); // 使用正确的值
            payload.setSystemInfo(systemInfo);

            // When
            concreteService.execute(payload);

            // Then
            verify(visaAuthDetailDataModifyService, never()).addAuthorizationLog(authRecordedDTO);
            verify(visaAuthDataUpdateManager).updateAuthorization(payload);
            verify(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicC(payload);
            assertEquals(AuthTrancactionStatusEnum.ERROR_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
        });
    }

    @Test
    @DisplayName("测试execute - SystemInfo为空")
    void testExecute_WithNullSystemInfo() {
        executeWithStaticMocks(() -> {
            // Given
            authRecordedDTO.setAuthResponseCode("05"); // 拒绝码
            payload.setSystemInfo(null);

            // When
            concreteService.execute(payload);

            // Then
            verify(visaAuthDetailDataModifyService, never()).addAuthorizationLog(authRecordedDTO);
            verify(visaAuthDataUpdateManager).updateAuthorization(payload);
            verify(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicC(payload);
            assertEquals(AuthTrancactionStatusEnum.ERROR_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
        });
    }

    @Test
    @DisplayName("测试applyBefore - 默认实现")
    void testApplyBefore() {
        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> concreteService.applyBefore(payload));
    }

    @Test
    @DisplayName("测试applyAfter - 默认实现")
    void testApplyAfter() {
        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> concreteService.applyAfter(payload));
    }

    @Test
    @DisplayName("测试applyUpdSucBefore - 默认实现")
    void testApplyUpdSucBefore() {
        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> concreteService.applyUpdSucBefore(payload));
    }

    @Test
    @DisplayName("测试applyUpdFailBefore - 默认实现")
    void testApplyUpdFailBefore() {
        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> concreteService.applyUpdFailBefore(payload));
    }

    @Test
    @DisplayName("测试setAuthProcessUpdDataService")
    void testSetAuthProcessUpdDataService() {
        // Given
        AuthProcessUpdDataService mockService = mock(AuthProcessUpdDataService.class);

        // When
        concreteService.setAuthProcessUpdDataService(mockService);

        // Then - 验证设置成功，通过执行成功流程来验证
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        concreteService.execute(payload);
        verify(mockService).updateData(payload);
    }

    @Test
    @DisplayName("测试anticipatedAmountVerification - 不同货币代码且金额不足")
    void testAnticipatedAmountVerification_DifferentCurrencyInsufficientAmount() {
        executeWithStaticMocks(() -> {
            // Given
            authRecordedDTO.setAuthAdditionalAmounts("004484************500000"); // 840是美元
            authRecordedDTO.setAuthBillingCurrencyCode("702"); // 新加坡元
            AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
            accountStatisticsInfo.setBalance(new BigDecimal("100.00"));

            // Mock货币代码查询
            com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCurrencyCode currencyCode =
                new com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCurrencyCode();
            currencyCode.setDecimalPlace(2);

            // Mock汇率查询
            com.anytech.anytxn.parameter.base.common.domain.model.system.ParmCurrencyRate currencyRate =
                new com.anytech.anytxn.parameter.base.common.domain.model.system.ParmCurrencyRate();
            currencyRate.setRateValue(135000L); // 1.35的汇率
            currencyRate.setExponent(5);

            lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                    .thenReturn(accountStatisticsInfo);
            lenient().when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("84"))
                    .thenReturn(currencyCode);
            lenient().when(parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(anyString(), eq("84"), eq("702"), eq("0")))
                    .thenReturn(currencyRate);

            // When
            concreteService.anticipatedAmountVerification(payload);

            // Then
            assertEquals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(), authRecordedDTO.getAuthResponseCode());
        });
    }

    @Test
    @DisplayName("测试anticipatedAmountVerification - 货币代码为空")
    void testAnticipatedAmountVerification_NullCurrencyCode() {
        executeWithStaticMocks(() -> {
            // Given
            authRecordedDTO.setAuthAdditionalAmounts("004484************100000");
            authRecordedDTO.setAuthBillingCurrencyCode("702");
            AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
            accountStatisticsInfo.setBalance(new BigDecimal("1000.00"));

            lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                    .thenReturn(accountStatisticsInfo);
            lenient().when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("84"))
                    .thenReturn(null);

            // When
            concreteService.anticipatedAmountVerification(payload);

            // Then
            assertEquals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(), authRecordedDTO.getAuthResponseCode());
        });
    }

    @Test
    @DisplayName("测试anticipatedAmountVerification - 汇率为空")
    void testAnticipatedAmountVerification_NullExchangeRate() {
        executeWithStaticMocks(() -> {
            // Given
            authRecordedDTO.setAuthAdditionalAmounts("004484************100000");
            authRecordedDTO.setAuthBillingCurrencyCode("702");
            AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
            accountStatisticsInfo.setBalance(new BigDecimal("1000.00"));

            com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCurrencyCode currencyCode =
                new com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCurrencyCode();
            currencyCode.setDecimalPlace(2);

            lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                    .thenReturn(accountStatisticsInfo);
            lenient().when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("84"))
                    .thenReturn(currencyCode);
            lenient().when(parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(anyString(), eq("84"), eq("702"), eq("0")))
                    .thenReturn(null);

            // When
            concreteService.anticipatedAmountVerification(payload);

            // Then
            assertEquals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(), authRecordedDTO.getAuthResponseCode());
        });
    }

    @Test
    @DisplayName("测试setResF054Value - 账户ID为空")
    void testSetResF054Value_WithBlankAccountId() {
        executeWithStaticMocks(() -> {
            // Given
            accountManagementInfoDTO.setAccountManagementId("");

            // When
            concreteService.setResF054Value(payload);

            // Then
            assertNotNull(authRecordedDTO.getAuthAdditionalAmounts());
            assertTrue(authRecordedDTO.getAuthAdditionalAmounts().contains("************")); // 零金额
        });
    }

    @Test
    @DisplayName("测试setResF054Value - 处理代码和货币代码组合")
    void testSetResF054Value_ProcessingCodeAndCurrencyCode() {
        executeWithStaticMocks(() -> {
            // Given
            authRecordedDTO.setAuthProcessingCode("001000");
            authRecordedDTO.setAuthBillingCurrencyCode("840");
            AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
            accountStatisticsInfo.setBalance(new BigDecimal("1234.56"));

            lenient().when(accountStatisticsInfoSelfMapper.selectByIdAndTypeAndOrgan(anyString(), anyString(), anyString()))
                    .thenReturn(accountStatisticsInfo);

            // When
            concreteService.setResF054Value(payload);

            // Then
            String additionalAmounts = authRecordedDTO.getAuthAdditionalAmounts();
            assertNotNull(additionalAmounts);
            // 验证格式正确即可，不验证具体内容
            assertTrue(additionalAmounts.length() > 0);
        });
    }
}