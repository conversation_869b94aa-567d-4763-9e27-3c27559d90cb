package com.anytech.anytxn.authorization.service.manager;

import com.anytech.anytxn.authorization.client.anycloud.AnyCloudFraudFeignClient;
import com.anytech.anytxn.authorization.client.encryption.AuthEncryptionFeignClient;
import com.anytech.anytxn.authorization.service.auth.AuthTransactionFeeServiceImpl;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.service.rule.RuleTransferImpl;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthCheckControlService;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmKeysInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.unicast.system.ParmSysClassSelfMapper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * AuthCheckManager单元测试
 * <AUTHOR>
 * @date 2025/07/15
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthCheckManager单元测试")
class AuthCheckManagerTest {

    @Mock
    private AuthTransactionFeeServiceImpl authTransactionFeeService;

    @Mock
    private AnyCloudFraudFeignClient anyCloudFraudFeignClient;

    @Mock
    private AuthEncryptionFeignClient authEncryptionFeignClient;

    @Mock
    private RuleTransferImpl ruleTransfer;

    @Mock
    private IAuthCheckControlService authCheckControlService;

    @Mock
    private ParmKeysInfoSelfMapper keysInfoSelfMapper;

    @Mock
    private ParmSysClassSelfMapper sysClassSelfMapper;

    @InjectMocks
    private AuthCheckManager authCheckManager;

    private AuthRecordedDTO authRecordedDTO;
    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;

    @BeforeEach
    void setUp() {
        // 测试数据将在各个测试方法中初始化，以避免OrgNumberUtils的null问题
    }

    private AuthRecordedDTO createTestAuthRecordedDTO() {
        AuthRecordedDTO dto = new AuthRecordedDTO();
        dto.setAuthResponseCode("00");
        dto.setAuthTransactionTypeDetailCode("C504");
        dto.setAuthTransactionTypeCode("01");
        dto.setAuthCardNumber("****************");
        dto.setAuthCardSequenceNumber("001");
        dto.setAuthSystemRelatedData("9F2608123456789F3602001");
        return dto;
    }

    @Test
    @DisplayName("判断是否是销户类授权交易 - 销户交易返回true")
    void isCancelAccount_WithCancelAccountTransaction_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            String authTransactionTypeDetailCode = "C504";
            String authTransactionTypeCode = "01";

            // Act
            boolean result = AuthCheckManager.isCancelAccount(authTransactionTypeDetailCode, authTransactionTypeCode);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("判断是否是销户类授权交易 - 撤销交易返回false")
    void isCancelAccount_WithRevocationTransaction_ShouldReturnFalse() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            String authTransactionTypeDetailCode = "C504";
            String authTransactionTypeCode = AuthTransTypeEnum.REVOCATION_TRANS.getCode();

            // Act
            boolean result = AuthCheckManager.isCancelAccount(authTransactionTypeDetailCode, authTransactionTypeCode);

            // Assert
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("判断是否是销户类授权交易 - 冲正交易返回false")
    void isCancelAccount_WithReversalTransaction_ShouldReturnFalse() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            String authTransactionTypeDetailCode = "C504";
            String authTransactionTypeCode = AuthTransTypeEnum.REVERSAL_TRANS.getCode();

            // Act
            boolean result = AuthCheckManager.isCancelAccount(authTransactionTypeDetailCode, authTransactionTypeCode);

            // Assert
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("校验结果是否成功 - 成功响应码返回true")
    void isSuccess_WithApprovedResponse_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());

            // Act
            boolean result = AuthCheckManager.isSuccess(authRecordedDTO);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("校验结果是否成功 - 失败响应码返回false")
    void isSuccess_WithFailedResponse_ShouldReturnFalse() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode("05");

            // Act
            boolean result = AuthCheckManager.isSuccess(authRecordedDTO);

            // Assert
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("校验结果是否失败 - 失败响应码返回true")
    void isFailure_WithFailedResponse_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode("05");

            // Act
            boolean result = AuthCheckManager.isFailure(authRecordedDTO);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("MasterCard成功校验 - 包含成功响应码返回true")
    void mcIsSuccess_WithMcApprovedResponse_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode("00");

            // Act
            boolean result = AuthCheckManager.mcIsSuccess(authRecordedDTO);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("MasterCard成功校验 - 不包含成功响应码返回false")
    void mcIsSuccess_WithNonMcApprovedResponse_ShouldReturnFalse() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode("05");

            // Act
            boolean result = AuthCheckManager.mcIsSuccess(authRecordedDTO);

            // Assert
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("UPI成功校验 - CHECK_RESPONSE响应码返回true")
    void upiIsSuccess_WithCheckResponse_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());

            // Act
            boolean result = AuthCheckManager.upiIsSuccess(authRecordedDTO);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("VISA成功校验 - 包含VISA成功响应码返回true")
    void visaIsSuccess_WithVisaApprovedResponse_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode("00");

            // Act
            boolean result = AuthCheckManager.visaIsSuccess(authRecordedDTO);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("验证AuthCheckManager是Spring服务组件")
    void testIsSpringService() {
        // Assert - 验证类上有@Service注解
        assertTrue(AuthCheckManager.class.isAnnotationPresent(org.springframework.stereotype.Service.class));
    }



    @Test
    @DisplayName("验证AuthCheckManager类存在")
    void testAuthCheckManagerClassExists() {
        // Assert
        assertNotNull(AuthCheckManager.class);
        assertEquals("AuthCheckManager", AuthCheckManager.class.getSimpleName());
    }

    @Test
    @DisplayName("验证常量定义正确")
    void testConstants() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();

            // Assert - 验证常量存在且不为空
            assertNotNull(AuthCheckManager.APPROVE_RESPONSE_CODE_MC);
            assertFalse(AuthCheckManager.APPROVE_RESPONSE_CODE_MC.isEmpty());
            assertTrue(AuthCheckManager.APPROVE_RESPONSE_CODE_MC.contains("00"));
        }
    }

    @Test
    @DisplayName("校验结果是否失败 - 成功响应码返回false")
    void isFailure_WithSuccessResponse_ShouldReturnFalse() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());

            // Act
            boolean result = AuthCheckManager.isFailure(authRecordedDTO);

            // Assert
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("校验结果是否失败 - 通过响应码枚举判断失败")
    void isFailure_WithAuthResponseCodeEnum_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            AuthResponseCodeEnum failureCode = AuthResponseCodeEnum.TRANSACTION_NOT_PERMITTED_TO_CARDHOLDER;

            // Act
            boolean result = AuthCheckManager.isFailure(failureCode);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("校验结果是否失败 - 通过响应码枚举判断成功")
    void isFailure_WithSuccessAuthResponseCodeEnum_ShouldReturnFalse() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            AuthResponseCodeEnum successCode = AuthResponseCodeEnum.APPROVED_BY_ISSUER;

            // Act
            boolean result = AuthCheckManager.isFailure(successCode);

            // Assert
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("MasterCard失败校验 - 失败响应码返回true")
    void mcIsFailure_WithFailedResponse_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode("05");

            // Act
            boolean result = AuthCheckManager.mcIsFailure(authRecordedDTO);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("MasterCard失败校验 - 成功响应码返回false")
    void mcIsFailure_WithSuccessResponse_ShouldReturnFalse() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode("00");

            // Act
            boolean result = AuthCheckManager.mcIsFailure(authRecordedDTO);

            // Assert
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("UPI成功校验 - 通过响应码字符串判断成功")
    void upiIsSuccess_WithResponseCodeString_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            String responseCode = AuthResponseCodeEnum.CHECK_RESPONSE.getCode();

            // Act
            boolean result = AuthCheckManager.upiIsSuccess(responseCode);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("UPI成功校验 - 通过响应码字符串判断失败")
    void upiIsSuccess_WithFailedResponseCodeString_ShouldReturnFalse() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            String responseCode = "05";

            // Act
            boolean result = AuthCheckManager.upiIsSuccess(responseCode);

            // Assert
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("UPI失败校验 - 失败响应码返回true")
    void upiIsFailure_WithFailedResponse_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode("05");

            // Act
            boolean result = AuthCheckManager.UpiIsFailure(authRecordedDTO);

            // Assert
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("UPI失败校验 - 成功响应码返回false")
    void upiIsFailure_WithSuccessResponse_ShouldReturnFalse() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());

            // Act
            boolean result = AuthCheckManager.UpiIsFailure(authRecordedDTO);

            // Assert
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("VISA成功校验 - 不包含VISA成功响应码返回false")
    void visaIsSuccess_WithNonVisaApprovedResponse_ShouldReturnFalse() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode("05");

            // Act
            boolean result = AuthCheckManager.visaIsSuccess(authRecordedDTO);

            // Assert
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("VISA失败校验 - 失败响应码返回true")
    void visaIsFailure_WithFailedResponse_ShouldReturnTrue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO = createTestAuthRecordedDTO();
            authRecordedDTO.setAuthResponseCode("05");

            // Act
            boolean result = AuthCheckManager.visaIsFailure(authRecordedDTO);

            // Assert
            assertTrue(result);
        }
    }
}
