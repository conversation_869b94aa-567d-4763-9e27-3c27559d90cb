package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.config.FileUpdateScheduleProperties;
import com.anytech.anytxn.authorization.base.domain.dto.*;
import com.anytech.anytxn.authorization.mapper.fileupdate.FileUpdateMapper;
import com.anytech.anytxn.authorization.base.domain.model.AuthorizationFileUpdate;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * @description AuthFileUpdateServiceImpl的单元测试类 - 简化版本
 * <AUTHOR>
 * @date 2025/01/08
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("授权文件更新服务测试")
class AuthFileUpdateServiceImplTest {

    @Mock
    private FileUpdateMapper fileUpdateMapper;

    @Mock
    private FileUpdateScheduleProperties fileUpdateScheduleProperties;

    @InjectMocks
    private AuthFileUpdateServiceImpl authFileUpdateService;

    @BeforeEach
    void setUp() {
        // 基础设置
    }

    @Test
    @DisplayName("异常场景 - 消息类型为空")
    void testDealFileUpdate_NullSourceCode() {
        // Arrange
        FileUpdateReqDTO fileUpdateReqDTO = new FileUpdateReqDTO();
        fileUpdateReqDTO.setSourceCode(null);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.dealFileUpdate(fileUpdateReqDTO);
        });
    }

    @Test
    @DisplayName("测试基本方法调用")
    void testBasicMethodCalls() {
        // 测试afterPropertiesSet方法
        assertDoesNotThrow(() -> {
            authFileUpdateService.afterPropertiesSet();
        });
    }

    @Test
    @DisplayName("成功场景 - 查询文件更新详情不存在")
    void testDetail_NotFound() {
        // Arrange
        String id = "FILE_UPDATE_001";

        when(fileUpdateMapper.detail(id)).thenReturn(null);

        // Act
        FileUpdateInfoDTO result = authFileUpdateService.detail(id);

        // Assert
        assertNull(result);
        verify(fileUpdateMapper).detail(id);
    }

    @Test
    @DisplayName("异常场景 - 查询详情ID为空")
    void testDetail_BlankId() {
        // Arrange
        String id = "";

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.detail(id);
        });

        verify(fileUpdateMapper, never()).detail(anyString());
    }

    @Test
    @DisplayName("异常场景 - 根据ID重发ID为空")
    void testResendById_BlankId() {
        // Arrange
        String id = "";

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.resendById(id);
        });

        verify(fileUpdateMapper, never()).detail(anyString());
    }

    @Test
    @DisplayName("失败场景 - 根据ID重发消息不存在")
    void testResendById_NotFound() {
        // Arrange
        String id = "FILE_UPDATE_001";

        when(fileUpdateMapper.detail(id)).thenReturn(null);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authFileUpdateService.resendById(id);
        });

        verify(fileUpdateMapper).detail(id);
    }


}