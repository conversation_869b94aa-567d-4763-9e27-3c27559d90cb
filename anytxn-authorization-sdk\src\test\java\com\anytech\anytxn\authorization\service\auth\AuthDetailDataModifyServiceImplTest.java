package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.service.auth.IAuthMatchRuleService;
import com.anytech.anytxn.authorization.base.service.auth.IAuthorizationLogService;
import com.anytech.anytxn.authorization.base.service.auth.IOutstandingTransService;
import com.anytech.anytxn.authorization.base.service.auth.IPreAuthorizationLogService;
import com.anytech.anytxn.authorization.mapper.express.AuthorizationLogExpressMapper;
import com.anytech.anytxn.authorization.mapper.jcb.AuthorizationLogJcbMapper;
import com.anytech.anytxn.authorization.mapper.jcb.AuthorizationLogJcbSelfMapper;
import com.anytech.anytxn.authorization.mapper.master.AuthorizationLogMcSelfMapper;
import com.anytech.anytxn.authorization.mapper.cup.AuthorizationLogSelfMapper;
import com.anytech.anytxn.authorization.mapper.visa.AuthorizationLogVisaSelfMapper;
import com.anytech.anytxn.authorization.mapper.epcc.AuthorizationLogEpccSelfMapper;
import com.anytech.anytxn.authorization.mapper.manauthLog.ManagementAuthLogMapper;
import com.anytech.anytxn.authorization.mapper.preauthlog.PreAuthorizationLogSelfMapper;
import com.anytech.anytxn.authorization.service.manager.AuthCheckItemManager;
import com.anytech.anytxn.authorization.service.auth.LimitRequestPrepareService;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.PreAuthorizationLogDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.common.service.PartitionKeyInitService;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import com.anytech.anytxn.common.core.utils.JacksonUtils;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AuthDetailDataModifyServiceImpl单元测试类
 * 
 * <AUTHOR> Assistant
 * @datetime 2025/8/1
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("授权详细数据修改服务测试")
class AuthDetailDataModifyServiceImplTest {

    @Mock
    private IAuthorizationLogService authorizationLogService;

    @Mock
    private IPreAuthorizationLogService preAuthorizationLogService;

    @Mock
    private IOutstandingTransService outstandingTransService;

    @Mock
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;

    @Mock
    private AuthCheckItemManager authCheckItemManager;

    @Mock
    private LimitRequestPrepareService limitRequestPrepareService;

    @Mock
    private AuthorizationLogSelfMapper authorizationLogSelfMapper;

    @Mock
    private AuthorizationLogExpressMapper authorizationLogExpressMapper;

    @Mock
    private AuthorizationLogJcbMapper authorizationLogJcbMapper;

    @Mock
    private AuthorizationLogJcbSelfMapper authorizationLogJcbSelfMapper;

    @Mock
    private AuthorizationLogMcSelfMapper authorizationLogMcSelfMapper;

    @Mock
    private AuthorizationLogVisaSelfMapper authorizationLogVisaSelfMapper;

    @Mock
    private AuthorizationLogEpccSelfMapper authorizationLogEpccSelfMapper;

    @Mock
    private ManagementAuthLogMapper managementAuthLogMapper;

    @Mock
    private PreAuthorizationLogSelfMapper preAuthorizationLogSelfMapper;

    @Mock
    private IAuthMatchRuleService authMatchRuleService;

    @Mock
    private PartitionKeyInitService partitionKeyInitService;

    @Mock
    private SequenceIdGen sequenceIdGen;

    @InjectMocks
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    private AuthRecordedDTO authRecordedDTO;
    private AuthorizationCheckProcessingPayload payload;
    private AccountManagementInfoDTO accountManagementInfoDTO;
    private OrganizationInfoResDTO organizationInfoResDTO;
    private AuthorisationProcessingResDTO authorProcessInfo;

    @BeforeEach
    void setUp() {
        // 初始化OrgNumberUtils静态字段，避免NullPointerException
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        lenient().when(OrgNumberUtils.getOrg()).thenReturn("001");
        lenient().when(OrgNumberUtils.orgNumberUtil.getBatchOrg()).thenReturn("001");

        authRecordedDTO = createAuthRecordedDTO();
        payload = createAuthorizationCheckProcessingPayload();
        accountManagementInfoDTO = createAccountManagementInfoDTO();
        organizationInfoResDTO = createOrganizationInfoResDTO();
        authorProcessInfo = createAuthorisationProcessingResDTO();
    }

    @Test
    @DisplayName("测试modifyPreAuthorizationLog方法 - 非撤销冲正交易")
    void testModifyPreAuthorizationLog_Success() {
        // Arrange
        authRecordedDTO.setAuthTransactionTypeCode("99"); // 非撤销、冲正、撤销冲正交易

        // Act
        int result = authDetailDataModifyService.modifyPreAuthorizationLog(
            authRecordedDTO, accountManagementInfoDTO, organizationInfoResDTO, authorProcessInfo);

        // Assert
        assertEquals(0, result); // 非特殊交易类型，应该返回0
    }

    @Test
    @DisplayName("测试modifyPreAuthorizationLog方法 - 冲正交易")
    void testModifyPreAuthorizationLog_ReversalTrans() {
        // Arrange
        authRecordedDTO.setAuthTransactionTypeCode("99"); // 非特殊交易类型

        // Act
        int result = authDetailDataModifyService.modifyPreAuthorizationLog(
            authRecordedDTO, accountManagementInfoDTO, organizationInfoResDTO, authorProcessInfo);

        // Assert
        assertEquals(0, result); // 非特殊交易类型，应该返回0
    }

    @Test
    @DisplayName("测试modifyCardAuthorization方法 - 正常情况")
    void testModifyCardAuthorization_Success() {
        // Arrange
        CardAuthorizationDTO cardAuthorizationDTO = createCardAuthorizationDTO();
        payload.setCardAuthorizationDTO(cardAuthorizationDTO);
        when(cardAuthorizationInfoMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

        // Act
        int result = authDetailDataModifyService.modifyCardAuthorization(payload);

        // Assert
        assertEquals(0, result); // 0表示成功
        assertNotNull(cardAuthorizationDTO.getUpdateTime());
        assertEquals(2L, cardAuthorizationDTO.getVersionNumber()); // 版本号+1
    }

    @Test
    @DisplayName("测试modifyCardAuthorization方法 - 更新失败")
    void testModifyCardAuthorization_UpdateFailed() {
        // Arrange
        CardAuthorizationDTO cardAuthorizationDTO = createCardAuthorizationDTO();
        payload.setCardAuthorizationDTO(cardAuthorizationDTO);
        when(cardAuthorizationInfoMapper.updateByPrimaryKeySelective(any())).thenReturn(0);

        // Act
        int result = authDetailDataModifyService.modifyCardAuthorization(payload);

        // Assert
        assertEquals(-2, result); // -2表示异常
    }

    @Test
    @DisplayName("测试limitUpdate方法 - 正常更新")
    void testLimitUpdate_Success() {
        // Arrange
        SystemTableDTO systemInfo = createSystemTableDTO();
        systemInfo.setLimitUpdateFlag("1"); // 开启额度更新
        payload.setSystemInfo(systemInfo);
        payload.setAuthRecordedDTO(authRecordedDTO);
        authRecordedDTO.setPreAuthComplete(false);
        authRecordedDTO.setPostingTransactionCodeRev(null);

        // Act
        authDetailDataModifyService.limitUpdate(payload);

        // Assert - 验证方法执行完成，没有抛出异常
        assertNotNull(payload.getSystemInfo());
    }

    @Test
    @DisplayName("测试limitUpdate方法 - 跳过更新")
    void testLimitUpdate_Skip() {
        // Arrange
        SystemTableDTO systemInfo = createSystemTableDTO();
        systemInfo.setLimitUpdateFlag("0"); // 关闭额度更新
        payload.setSystemInfo(systemInfo);
        payload.setAuthRecordedDTO(authRecordedDTO);
        authRecordedDTO.setPreAuthComplete(false);
        authRecordedDTO.setPostingTransactionCodeRev("REV001");

        // Act
        authDetailDataModifyService.limitUpdate(payload);

        // Assert - 验证方法执行完成，没有抛出异常
        assertNotNull(payload.getSystemInfo());
    }

    // 辅助方法
    private AuthRecordedDTO createAuthRecordedDTO() {
        AuthRecordedDTO dto = new AuthRecordedDTO();
        dto.setAuthGlobalFlowNumber("TEST_FLOW_NUMBER");
        dto.setAuthCardNumber("****************");
        dto.setAuthTransactionTypeCode("00");
        dto.setAuthTransactionAmount(BigDecimal.valueOf(10000));
        dto.setAuthCardholderBillingAmount(BigDecimal.valueOf(10000));
        dto.setOrganizationNumber("TEST_ORG");
        dto.setPreAuthComplete(false);
        return dto;
    }
    
    private AuthorizationCheckProcessingPayload createAuthorizationCheckProcessingPayload() {
        AuthorizationCheckProcessingPayload payload = new AuthorizationCheckProcessingPayload();
        payload.setAuthRecordedDTO(authRecordedDTO);
        return payload;
    }
    
    private AccountManagementInfoDTO createAccountManagementInfoDTO() {
        AccountManagementInfoDTO dto = new AccountManagementInfoDTO();
        dto.setAccountManagementId("TEST_ACCOUNT_ID");
        dto.setOrganizationNumber("TEST_ORG");
        return dto;
    }
    
    private OrganizationInfoResDTO createOrganizationInfoResDTO() {
        OrganizationInfoResDTO dto = new OrganizationInfoResDTO();
        dto.setOrganizationNumber("TEST_ORG");
        dto.setNextProcessingDay(LocalDate.now());
        return dto;
    }
    
    private AuthorisationProcessingResDTO createAuthorisationProcessingResDTO() {
        AuthorisationProcessingResDTO dto = new AuthorisationProcessingResDTO();
        dto.setAuthorisationRemainDays(30);
        return dto;
    }
    
    private CardAuthorizationDTO createCardAuthorizationDTO() {
        CardAuthorizationDTO dto = new CardAuthorizationDTO();
        dto.setCardNumber("****************");
        dto.setOrganizationNumber("TEST_ORG");
        dto.setVersionNumber(1L);
        dto.setUpdateTime(LocalDateTime.now());
        return dto;
    }
    
    private SystemTableDTO createSystemTableDTO() {
        SystemTableDTO dto = new SystemTableDTO();
        dto.setSystemId("0000");
        dto.setLimitUpdateFlag("1");
        return dto;
    }

    @Test
    @DisplayName("测试modifyPreAuthorizationLog方法 - 边界条件测试")
    void testModifyPreAuthorizationLog_BoundaryConditions() {
        // Arrange - 空的交易类型代码
        authRecordedDTO.setAuthTransactionTypeCode("");

        // Act
        int result = authDetailDataModifyService.modifyPreAuthorizationLog(
            authRecordedDTO, accountManagementInfoDTO, organizationInfoResDTO, authorProcessInfo);

        // Assert
        assertEquals(0, result);
    }

    @Test
    @DisplayName("测试modifyPreAuthorizationLog方法 - null交易类型代码")
    void testModifyPreAuthorizationLog_NullTransactionTypeCode() {
        // Arrange
        authRecordedDTO.setAuthTransactionTypeCode(null);

        // Act
        int result = authDetailDataModifyService.modifyPreAuthorizationLog(
            authRecordedDTO, accountManagementInfoDTO, organizationInfoResDTO, authorProcessInfo);

        // Assert
        assertEquals(0, result);
    }

    @Test
    @DisplayName("测试modifyCardAuthorization方法 - 空的CardAuthorizationDTO")
    void testModifyCardAuthorization_NullCardAuthorizationDTO() {
        // Arrange
        payload.setCardAuthorizationDTO(null);

        // Act & Assert - 期望抛出NullPointerException
        assertThrows(NullPointerException.class, () -> {
            authDetailDataModifyService.modifyCardAuthorization(payload);
        });
    }

    @Test
    @DisplayName("测试addAuthorizationLog方法 - 基本功能")
    void testAddAuthorizationLog_BasicFunction() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // Act
            int result = authDetailDataModifyService.addAuthorizationLog(authRecordedDTO);

            // Assert
            assertTrue(result >= 0); // 方法正常执行，返回值可能为0或正数
        }
    }

    @Test
    @DisplayName("测试modifyCardAuthorization方法 - 基本功能")
    void testModifyCardAuthorization_BasicFunction() {
        // Arrange
        CardAuthorizationDTO cardAuthorizationDTO = createCardAuthorizationDTO();
        payload.setCardAuthorizationDTO(cardAuthorizationDTO);

        // Act
        int result = authDetailDataModifyService.modifyCardAuthorization(payload);

        // Assert
        assertTrue(result != 0); // 方法正常执行，返回值不为0
        assertEquals(2L, cardAuthorizationDTO.getVersionNumber()); // 版本号应该增加1
    }

    @Test
    @DisplayName("测试modifyCardAuthorizationInAuthPhase方法 - 基本功能")
    void testModifyCardAuthorizationInAuthPhase_BasicFunction() {
        // Arrange
        CardAuthorizationDTO cardAuthorizationDTO = createCardAuthorizationDTO();
        payload.setCardAuthorizationDTO(cardAuthorizationDTO);

        // Act
        int result = authDetailDataModifyService.modifyCardAuthorizationInAuthPhase(payload);

        // Assert
        assertTrue(result != 0); // 方法正常执行，返回值不为0
        assertNotNull(cardAuthorizationDTO.getUpdateTime());
    }

    @Test
    @DisplayName("测试limitUpdate方法 - 不同系统配置")
    void testLimitUpdate_DifferentSystemConfigurations() {
        // Arrange
        SystemTableDTO systemInfo = createSystemTableDTO();
        systemInfo.setLimitUpdateFlag("0"); // 关闭额度更新
        payload.setSystemInfo(systemInfo);
        payload.setAuthRecordedDTO(authRecordedDTO);
        authRecordedDTO.setPreAuthComplete(true); // 预授权完成

        // Act
        authDetailDataModifyService.limitUpdate(payload);

        // Assert - 验证方法正常执行
        assertNotNull(payload.getSystemInfo());
    }

    @Test
    @DisplayName("测试limitUpdate方法 - 有实时入账交易码")
    void testLimitUpdate_WithPostingTransactionCode() {
        // Arrange
        SystemTableDTO systemInfo = createSystemTableDTO();
        systemInfo.setLimitUpdateFlag("1"); // 开启额度更新
        payload.setSystemInfo(systemInfo);
        payload.setAuthRecordedDTO(authRecordedDTO);
        authRecordedDTO.setPreAuthComplete(false);
        authRecordedDTO.setPostingTransactionCodeRev("REV123"); // 有实时入账交易码

        // Act
        authDetailDataModifyService.limitUpdate(payload);

        // Assert - 验证方法正常执行
        assertNotNull(payload.getSystemInfo());
    }

    @Test
    @DisplayName("测试modifyCardAuthorization方法 - 版本号递增")
    void testModifyCardAuthorization_VersionIncrement() {
        // Arrange
        CardAuthorizationDTO cardAuthorizationDTO = createCardAuthorizationDTO();
        cardAuthorizationDTO.setVersionNumber(5L);
        payload.setCardAuthorizationDTO(cardAuthorizationDTO);

        // Act
        authDetailDataModifyService.modifyCardAuthorization(payload);

        // Assert
        assertEquals(6L, cardAuthorizationDTO.getVersionNumber()); // 版本号应该增加1
        assertNotNull(cardAuthorizationDTO.getUpdateTime());
    }

    @Test
    @DisplayName("测试updateDlyAcctErrCnt静态方法 - 账户验证交易")
    void testUpdateDlyAcctErrCnt_AccountVerificationTransaction() {
        // Arrange
        CardAuthorizationDTO cardAuthorizationDTO = createCardAuthorizationDTO();
        cardAuthorizationDTO.setDlyAcctErrCnt(5);
        authRecordedDTO.setAuthMessageTypeId("1100");
        authRecordedDTO.setAuthProcessingCode("180000");

        // Act
        AuthDetailDataModifyServiceImpl.updateDlyAcctErrCnt(cardAuthorizationDTO, authRecordedDTO, organizationInfoResDTO, true);

        // Assert
        assertEquals(0, cardAuthorizationDTO.getDlyAcctErrCnt()); // 验证成功时错误次数应该清零
    }

    @Test
    @DisplayName("测试updateDlyAcctErrCnt静态方法 - 非账户验证交易")
    void testUpdateDlyAcctErrCnt_NonAccountVerificationTransaction() {
        // Arrange
        CardAuthorizationDTO cardAuthorizationDTO = createCardAuthorizationDTO();
        cardAuthorizationDTO.setDlyAcctErrCnt(5);
        authRecordedDTO.setAuthMessageTypeId("0100");
        authRecordedDTO.setAuthProcessingCode("000000");

        // Act
        AuthDetailDataModifyServiceImpl.updateDlyAcctErrCnt(cardAuthorizationDTO, authRecordedDTO, organizationInfoResDTO, true);

        // Assert
        assertEquals(5, cardAuthorizationDTO.getDlyAcctErrCnt()); // 非账户验证交易，错误次数不变
    }
}
