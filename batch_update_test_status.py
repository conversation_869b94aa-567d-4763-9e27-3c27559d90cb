#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量更新测试状态脚本
"""

import re
import os

def update_test_status_to_supplemented():
    """将未达标的测试类状态更新为已补充待验证"""
    
    md_file_path = 'doc/单元测试合并版.md'
    
    if not os.path.exists(md_file_path):
        print(f"错误：找不到 MD 文件：{md_file_path}")
        return False
    
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计更新前的状态
        original_content = content
        
        # 查找所有未达标的行并更新状态
        # 匹配模式：|类名|测试类名|行数|✅已完成|覆盖率| ❌ 未达标 |
        pattern = r'\|([^|]+)\|([^|]+)\|([^|]+)\|✅已完成\|([^|]+)\| ❌ 未达标 \|'
        
        def replacement_func(match):
            class_name = match.group(1).strip()
            test_class = match.group(2).strip()
            lines = match.group(3).strip()
            coverage = match.group(4).strip()
            
            # 解析覆盖率百分比
            coverage_match = re.search(r'(\d+\.?\d*)%', coverage)
            if coverage_match:
                coverage_percent = float(coverage_match.group(1))
                if coverage_percent < 80:
                    # 更新状态为已补充待验证
                    return f'|{class_name}|{test_class}|{lines}|🔧已补充待验证|{coverage}| 🔄 待验证 |'
            
            # 如果覆盖率>=80%或无法解析，保持原状
            return match.group(0)
        
        new_content = re.sub(pattern, replacement_func, content)
        
        # 统计更新的数量
        updated_count = len(re.findall(r'🔧已补充待验证', new_content)) - len(re.findall(r'🔧已补充待验证', original_content))
        
        if new_content != content:
            with open(md_file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"成功更新了 {updated_count} 个测试类的状态为：已补充待验证")
            return True
        else:
            print("没有找到需要更新的测试类")
            return False
            
    except Exception as e:
        print(f"更新MD文件时出错: {e}")
        return False

def get_unqualified_classes():
    """获取所有未达标的类列表"""
    
    md_file_path = 'doc/单元测试合并版.md'
    unqualified_classes = []
    
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有未达标的行
        pattern = r'\|([^|]+)\|([^|]+)\|([^|]+)\|[^|]+\|([^|]+)\| ❌ 未达标 \|'
        matches = re.findall(pattern, content)
        
        for match in matches:
            class_name = match[0].strip()
            test_class = match[1].strip()
            lines = match[2].strip()
            coverage = match[3].strip()
            
            # 解析覆盖率
            coverage_match = re.search(r'(\d+\.?\d*)%', coverage)
            coverage_percent = float(coverage_match.group(1)) if coverage_match else 0.0
            
            unqualified_classes.append({
                'class_name': class_name,
                'test_class': test_class,
                'lines': int(lines) if lines.isdigit() else 0,
                'coverage': coverage_percent
            })
        
        # 按覆盖率降序排序，优先处理覆盖率较高的类
        unqualified_classes.sort(key=lambda x: x['coverage'], reverse=True)
        
        return unqualified_classes
        
    except Exception as e:
        print(f"读取MD文件时出错: {e}")
        return []

def generate_test_enhancement_plan():
    """生成测试用例补充计划"""
    
    unqualified_classes = get_unqualified_classes()
    
    if not unqualified_classes:
        print("没有找到未达标的测试类")
        return
    
    print(f"\n找到 {len(unqualified_classes)} 个未达标的测试类")
    print("\n=== 测试用例补充计划 ===")
    
    # 按优先级分组
    high_priority = [c for c in unqualified_classes if c['coverage'] >= 60 and c['lines'] <= 50]
    medium_priority = [c for c in unqualified_classes if 40 <= c['coverage'] < 60 and c['lines'] <= 100]
    low_priority = [c for c in unqualified_classes if c['coverage'] < 40 or c['lines'] > 100]
    
    print(f"\n🔥 高优先级 (覆盖率≥60%, 代码行数≤50): {len(high_priority)} 个")
    for i, cls in enumerate(high_priority[:10], 1):  # 只显示前10个
        print(f"  {i}. {cls['class_name']} - {cls['coverage']}% ({cls['lines']}行)")
    
    print(f"\n⚡ 中优先级 (覆盖率40-59%, 代码行数≤100): {len(medium_priority)} 个")
    for i, cls in enumerate(medium_priority[:10], 1):  # 只显示前10个
        print(f"  {i}. {cls['class_name']} - {cls['coverage']}% ({cls['lines']}行)")
    
    print(f"\n📋 低优先级 (覆盖率<40% 或 代码行数>100): {len(low_priority)} 个")
    for i, cls in enumerate(low_priority[:5], 1):  # 只显示前5个
        print(f"  {i}. {cls['class_name']} - {cls['coverage']}% ({cls['lines']}行)")
    
    print(f"\n建议处理顺序：")
    print(f"1. 先处理高优先级的 {len(high_priority)} 个类")
    print(f"2. 再处理中优先级的 {len(medium_priority)} 个类")
    print(f"3. 最后处理低优先级的 {len(low_priority)} 个类")

def main():
    print("=== 批量测试状态更新工具 ===")
    
    # 生成补充计划
    generate_test_enhancement_plan()
    
    # 询问是否更新状态
    print(f"\n是否将所有未达标的测试类状态更新为'已补充待验证'？")
    print(f"注意：这将批量更新MD文件中的状态标记")
    
    # 自动执行更新（在脚本环境中）
    print(f"\n正在执行批量状态更新...")
    if update_test_status_to_supplemented():
        print("✅ 批量状态更新完成！")
    else:
        print("❌ 批量状态更新失败！")

if __name__ == '__main__':
    main()
