package com.anytech.anytxn.authorization.service.auth.visa;

import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.enums.TrasactionStatusEnum;
import com.anytech.anytxn.authorization.base.service.auth.IAuthorizationLogService;
import com.anytech.anytxn.authorization.base.service.auth.IOutstandingTransService;
import com.anytech.anytxn.authorization.service.channel.visa.VisaCheckAuthResponseCodeServiceImpl;
import com.anytech.anytxn.authorization.service.transaction.PostAccountServiceImpl;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTrancactionStatusEnum;
import com.anytech.anytxn.business.base.authorization.enums.ReversalTypeEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthorizationLogDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

/**
 * @description VisaAFDCompAdviceProcessUpdDataServiceImpl的单元测试类
 * <AUTHOR>
 * @date 2025/01/09
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Visa AFD完成通知处理更新数据服务测试")
class VisaAFDCompAdviceProcessUpdDataServiceImplTest {

    @Mock
    private VisaCheckAuthResponseCodeServiceImpl visaCheckAuthResponseCodeServiceImpl;

    @Mock
    private PostAccountServiceImpl postAccountService;

    @Mock
    private IAuthorizationLogService authorizationLogService;

    @Mock
    private IOutstandingTransService outstandingTransService;

    @InjectMocks
    private VisaAFDCompAdviceProcessUpdDataServiceImpl visaAFDCompAdviceProcessUpdDataService;

    private AuthorizationCheckProcessingPayload payload;
    private AuthRecordedDTO authRecordedDTO;
    private AuthorizationLogDTO origAuthorizationLogDTO;
    private OutstandingTransactionDTO outstandingTransactionDTO;

    @BeforeEach
    void setUp() {
        // 使用MockedStatic来Mock OrgNumberUtils，避免BaseEntity构造函数中的NullPointerException
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // 设置AuthRecordedDTO
            authRecordedDTO = new AuthRecordedDTO();
            authRecordedDTO.setAuthGlobalFlowNumber("GLOBAL123456");

            // 设置原始授权日志
            origAuthorizationLogDTO = new AuthorizationLogDTO();
            origAuthorizationLogDTO.setIdNumber("12345");
            origAuthorizationLogDTO.setGlobalFlowNumber("ORIG_FLOW123456");

            // 设置未清交易
            outstandingTransactionDTO = new OutstandingTransactionDTO();
            outstandingTransactionDTO.setTraceId("12345");
            outstandingTransactionDTO.setGlobalFlowNumber("ORIG_FLOW123456");

            // 设置处理负载
            payload = new AuthorizationCheckProcessingPayload();
            payload.setAuthRecordedDTO(authRecordedDTO);
            payload.setOrigAuthorizationLogDTO(origAuthorizationLogDTO);
        }
        payload.setOutstandingTransactionDTO(outstandingTransactionDTO);
    }

    @Test
    @DisplayName("测试更新数据 - 成功场景")
    void testUpdateData_Success() {
        // Arrange
        when(authorizationLogService.updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO)).thenReturn(1);
        when(outstandingTransService.update(outstandingTransactionDTO)).thenReturn(1);
        when(postAccountService.visaPostAccount(payload)).thenReturn("ORDER123456");

        // Act
        int result = visaAFDCompAdviceProcessUpdDataService.updateData(payload);

        // Assert
        assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
        assertThat(origAuthorizationLogDTO.getTrancactionStatus()).isEqualTo(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
        assertThat(origAuthorizationLogDTO.getReversalType()).isEqualTo(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
        assertThat(outstandingTransactionDTO.getTrasactionStatus()).isEqualTo(TrasactionStatusEnum.CANCEL.getCode());
        assertThat(authRecordedDTO.getOrderId()).isEqualTo("ORDER123456");

        verify(authorizationLogService).updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO);
        verify(outstandingTransService).update(outstandingTransactionDTO);
        verify(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicB(payload);
        verify(postAccountService).visaPostAccount(payload);
    }

    @Test
    @DisplayName("测试更新数据 - 授权日志更新失败")
    void testUpdateData_AuthorizationLogUpdateFailed() {
        // Arrange
        when(authorizationLogService.updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO)).thenReturn(0);

        // Act & Assert
        assertThatThrownBy(() -> visaAFDCompAdviceProcessUpdDataService.updateData(payload))
                .isInstanceOf(AnyTxnAuthException.class)
                .hasFieldOrPropertyWithValue("errCode", AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR.getCode());

        // 验证状态已设置但后续操作未执行
        assertThat(origAuthorizationLogDTO.getTrancactionStatus()).isEqualTo(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
        assertThat(origAuthorizationLogDTO.getReversalType()).isEqualTo(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());

        verify(authorizationLogService).updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO);
        verify(outstandingTransService, never()).update(any());
        verify(visaCheckAuthResponseCodeServiceImpl, never()).authDataUpdateLogicB(any());
        verify(postAccountService, never()).visaPostAccount(any());
    }

    @Test
    @DisplayName("测试更新数据 - 授权日志更新返回异常数量")
    void testUpdateData_AuthorizationLogUpdateInvalidCount() {
        // Arrange
        when(authorizationLogService.updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO)).thenReturn(2);

        // Act & Assert
        assertThatThrownBy(() -> visaAFDCompAdviceProcessUpdDataService.updateData(payload))
                .isInstanceOf(AnyTxnAuthException.class)
                .hasFieldOrPropertyWithValue("errCode", AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR.getCode());

        verify(authorizationLogService).updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO);
        verify(outstandingTransService, never()).update(any());
    }

    @Test
    @DisplayName("测试更新数据 - 未清交易更新成功但返回0")
    void testUpdateData_OutstandingTransactionUpdateReturnsZero() {
        // Arrange
        when(authorizationLogService.updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO)).thenReturn(1);
        when(outstandingTransService.update(outstandingTransactionDTO)).thenReturn(0);
        when(postAccountService.visaPostAccount(payload)).thenReturn("ORDER123456");

        // Act
        int result = visaAFDCompAdviceProcessUpdDataService.updateData(payload);

        // Assert - 未清交易更新不检查返回值，继续执行
        assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
        assertThat(outstandingTransactionDTO.getTrasactionStatus()).isEqualTo(TrasactionStatusEnum.CANCEL.getCode());

        verify(authorizationLogService).updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO);
        verify(outstandingTransService).update(outstandingTransactionDTO);
        verify(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicB(payload);
        verify(postAccountService).visaPostAccount(payload);
    }

    @Test
    @DisplayName("测试更新数据 - visa检查服务抛出异常")
    void testUpdateData_VisaCheckServiceThrowsException() {
        // Arrange
        when(authorizationLogService.updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO)).thenReturn(1);
        when(outstandingTransService.update(outstandingTransactionDTO)).thenReturn(1);
        doThrow(new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR))
                .when(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicB(payload);

        // Act & Assert
        assertThatThrownBy(() -> visaAFDCompAdviceProcessUpdDataService.updateData(payload))
                .isInstanceOf(AnyTxnAuthException.class)
                .hasFieldOrPropertyWithValue("errCode", AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR.getCode());

        verify(authorizationLogService).updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO);
        verify(outstandingTransService).update(outstandingTransactionDTO);
        verify(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicB(payload);
        verify(postAccountService, never()).visaPostAccount(any());
    }

    @Test
    @DisplayName("测试更新数据 - postAccount服务抛出异常")
    void testUpdateData_PostAccountServiceThrowsException() {
        // Arrange
        when(authorizationLogService.updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO)).thenReturn(1);
        when(outstandingTransService.update(outstandingTransactionDTO)).thenReturn(1);
        when(postAccountService.visaPostAccount(payload))
                .thenThrow(new RuntimeException("Post account failed"));

        // Act & Assert
        assertThatThrownBy(() -> visaAFDCompAdviceProcessUpdDataService.updateData(payload))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Post account failed");

        verify(authorizationLogService).updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO);
        verify(outstandingTransService).update(outstandingTransactionDTO);
        verify(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicB(payload);
        verify(postAccountService).visaPostAccount(payload);
    }

    @Test
    @DisplayName("测试更新数据 - postAccount返回null")
    void testUpdateData_PostAccountReturnsNull() {
        // Arrange
        when(authorizationLogService.updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO)).thenReturn(1);
        when(outstandingTransService.update(outstandingTransactionDTO)).thenReturn(1);
        when(postAccountService.visaPostAccount(payload)).thenReturn(null);

        // Act
        int result = visaAFDCompAdviceProcessUpdDataService.updateData(payload);

        // Assert
        assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
        assertThat(authRecordedDTO.getOrderId()).isNull();

        verify(authorizationLogService).updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO);
        verify(outstandingTransService).update(outstandingTransactionDTO);
        verify(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicB(payload);
        verify(postAccountService).visaPostAccount(payload);
    }

    @Test
    @DisplayName("测试更新数据 - 未清交易服务抛出异常")
    void testUpdateData_OutstandingTransactionServiceThrowsException() {
        // Arrange
        when(authorizationLogService.updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO)).thenReturn(1);
        doThrow(new RuntimeException("Outstanding transaction update failed"))
                .when(outstandingTransService).update(outstandingTransactionDTO);

        // Act & Assert
        assertThatThrownBy(() -> visaAFDCompAdviceProcessUpdDataService.updateData(payload))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Outstanding transaction update failed");

        verify(authorizationLogService).updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO);
        verify(outstandingTransService).update(outstandingTransactionDTO);
        verify(visaCheckAuthResponseCodeServiceImpl, never()).authDataUpdateLogicB(any());
        verify(postAccountService, never()).visaPostAccount(any());
    }

    @Test
    @DisplayName("测试更新数据 - 所有服务都返回成功")
    void testUpdateData_AllServicesSucceed() {
        // Arrange
        when(authorizationLogService.updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO)).thenReturn(1);
        when(outstandingTransService.update(outstandingTransactionDTO)).thenReturn(1);
        when(postAccountService.visaPostAccount(payload)).thenReturn("ORDER789");

        // Act
        int result = visaAFDCompAdviceProcessUpdDataService.updateData(payload);

        // Assert
        assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
        assertThat(origAuthorizationLogDTO.getTrancactionStatus()).isEqualTo(AuthTrancactionStatusEnum.REVOCATION_STATUS.getCode());
        assertThat(origAuthorizationLogDTO.getReversalType()).isEqualTo(ReversalTypeEnum.REVOCATION_REVOCATIONTRANS.getCode());
        assertThat(outstandingTransactionDTO.getTrasactionStatus()).isEqualTo(TrasactionStatusEnum.CANCEL.getCode());
        assertThat(authRecordedDTO.getOrderId()).isEqualTo("ORDER789");

        // 验证调用顺序
        var inOrder = inOrder(authorizationLogService, outstandingTransService, visaCheckAuthResponseCodeServiceImpl, postAccountService);
        inOrder.verify(authorizationLogService).updateAuthorizationLogByPrimaryId(origAuthorizationLogDTO);
        inOrder.verify(outstandingTransService).update(outstandingTransactionDTO);
        inOrder.verify(visaCheckAuthResponseCodeServiceImpl).authDataUpdateLogicB(payload);
        inOrder.verify(postAccountService).visaPostAccount(payload);
    }

    @Test
    @DisplayName("测试更新数据 - 处理null对象")
    void testUpdateData_HandleNullObjects() {
        // Arrange
        payload.setOrigAuthorizationLogDTO(null);
        payload.setOutstandingTransactionDTO(null);

        // Act & Assert
        assertThatThrownBy(() -> visaAFDCompAdviceProcessUpdDataService.updateData(payload))
                .isInstanceOf(NullPointerException.class);

        verify(authorizationLogService, never()).updateAuthorizationLogByPrimaryId(any());
        verify(outstandingTransService, never()).update(any());
        verify(visaCheckAuthResponseCodeServiceImpl, never()).authDataUpdateLogicB(any());
        verify(postAccountService, never()).visaPostAccount(any());
    }
} 