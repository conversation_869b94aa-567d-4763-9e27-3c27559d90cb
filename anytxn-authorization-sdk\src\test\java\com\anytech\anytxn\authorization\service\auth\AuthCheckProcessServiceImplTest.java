/**
 * @description AuthCheckProcessServiceImpl的单元测试类
 * <AUTHOR>
 * @date 2025/07/08
 * @version 1.0
 * @AI 已复核
 */
package com.anytech.anytxn.authorization.service.auth;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Future;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.domain.dto.DataResponse;
import com.anytech.anytxn.authorization.base.enums.AuthMessageTypeIdEnum;
import com.anytech.anytxn.authorization.service.channel.AbstractTransRoutingService;
import com.anytech.anytxn.authorization.base.service.manager.ApplicationManager;
import com.anytech.anytxn.authorization.service.manager.AuthCheckManager;
import com.anytech.anytxn.authorization.service.manager.AuthSmsManager;
import com.anytech.anytxn.authorization.service.manager.AuthThreadLocalManager;
import com.anytech.anytxn.authorization.service.manager.IntegralServiceManager;
import com.anytech.anytxn.authorization.service.transaction.PostAccountServiceImpl;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTransTypeEnum;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallTrialResDTO;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

@ExtendWith(MockitoExtension.class)
@DisplayName("AuthCheckProcessServiceImpl单元测试")
class AuthCheckProcessServiceImplTest {

    @InjectMocks
    private AuthCheckProcessServiceImpl authCheckProcessService;

    @Mock
    private AuthDataUpdateServiceImpl authDataUpdateService;
    
    @Mock
    private AuthCheckItemInspecProcessServiceImpl authCheckItemInspecProcessService;
    
    @Mock
    private AuthCheckDataPrepareServiceImpl authCheckDataPrepareService;
    
    @Mock
    private PostAccountServiceImpl postAccountService;
    
    @Mock
    private AuthSmsManager authSmsManager;
    
    @Mock
    private IntegralServiceManager integralServiceManager;
    
    @Mock
    private AuthCheckManager authCheckManager;
    
    @Mock
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    @Mock
    private AbstractTransRoutingService abstractTransRoutingService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private Future<DataResponse<Map<String, Object>>> dataResponseFuture;

    private AuthRecordedDTO authRecordedDTO;
    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;

    @BeforeEach
    void setUp() {
        // 在每个测试方法中创建测试数据，避免OrgNumberUtils问题
    }

    /**
     * 创建测试数据的帮助方法
     */
    private void createTestData() {
        authRecordedDTO = new AuthRecordedDTO();
        authRecordedDTO.setAuthCustomerId("CUSTOMER001");
        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        authRecordedDTO.setAuthTransType("0200");

        authorizationCheckProcessingPayload = new AuthorizationCheckProcessingPayload();
        authorizationCheckProcessingPayload.setAuthRecordedDTO(authRecordedDTO);
    }

    /**
     * 创建InstallTrialResDTO的帮助方法
     */
    private InstallTrialResDTO createInstallTrialResDTO() {
        InstallTrialResDTO installTrialResDTO = new InstallTrialResDTO();
        installTrialResDTO.setTotalFeeAmount(new BigDecimal("1000"));
        installTrialResDTO.setFirstTermAmount(new BigDecimal("500"));
        installTrialResDTO.setFirstTermFee(new BigDecimal("50"));
        installTrialResDTO.setTermFee(new BigDecimal("25"));
        return installTrialResDTO;
    }

    // ========== authCheck 方法测试 ==========

    @Test
    @DisplayName("测试authCheck方法-正常授权流程成功")
    void testAuthCheck_NormalTransactionSuccess() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);
            mockedAuthCheck.when(() -> AuthCheckManager.isSuccess(any(AuthRecordedDTO.class))).thenReturn(true);

            createTestData();

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(authCheckManager.apqcCheck(any(), eq(false))).thenReturn(authRecordedDTO);
            when(authCheckItemInspecProcessService.processAuthCheck(any(), any())).thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            doNothing().when(authCheckManager).getArpcValue(any(), eq(true));
            when(postAccountService.postAccount(any())).thenReturn("ORDER123");
            doNothing().when(authSmsManager).sendMsg(any());
            doNothing().when(integralServiceManager).integralService(any());

            // Mock ApplicationManager
            ApplicationManager.applicationContext = applicationContext;
            when(applicationContext.getBean(eq("0200_Service"), eq(AbstractTransRoutingService.class)))
                .thenReturn(abstractTransRoutingService);

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getOrderId()).isEqualTo("ORDER123");

            // 验证方法调用
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authCheckItemInspecProcessService).processAuthCheck(authorizationCheckProcessingPayload, dataResponseFuture);
            verify(postAccountService).postAccount(authorizationCheckProcessingPayload);
            verify(authSmsManager).sendMsg(authorizationCheckProcessingPayload);
            verify(integralServiceManager).integralService(authorizationCheckProcessingPayload);
        }
    }

    @Test
    @DisplayName("测试authCheck方法-数据准备失败")
    void testAuthCheck_DataPrepareFailure() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(true);

            createTestData();

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(authDetailDataModifyService.addAuthorizationLog(any())).thenReturn(1);

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // 验证方法调用
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            verify(authCheckItemInspecProcessService, never()).processAuthCheck(any(), any());
        }
    }

    @Test
    @DisplayName("测试authCheck方法-非正常交易跳过授权检查")
    void testAuthCheck_NonNormalTransactionSkipCheck() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);

            createTestData();
            // 设置为非正常交易
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVERSAL_TRANS.getCode());

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(postAccountService.postAccount(any())).thenReturn("ORDER456");
            doNothing().when(authSmsManager).sendMsg(any());
            doNothing().when(integralServiceManager).integralService(any());

            // Mock ApplicationManager
            ApplicationManager.applicationContext = applicationContext;
            when(applicationContext.getBean(eq("0200_Service"), eq(AbstractTransRoutingService.class)))
                .thenReturn(abstractTransRoutingService);

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 验证跳过了授权检查
            verify(authCheckItemInspecProcessService, never()).processAuthCheck(any(), any());
            verify(postAccountService).postAccount(authorizationCheckProcessingPayload);
        }
    }

    @Test
    @DisplayName("测试authCheck方法-APQC校验失败")
    void testAuthCheck_ApqcCheckFailure() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());

            createTestData();

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            
            // Mock APQC检查失败
            AuthRecordedDTO failureAuthRecordedDTO = new AuthRecordedDTO();
            when(authCheckManager.apqcCheck(any(), eq(false))).thenReturn(failureAuthRecordedDTO);
            
            // Mock isFailure: 对于原始的authRecordedDTO返回false，对于APQC返回的failureAuthRecordedDTO返回true
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(eq(authRecordedDTO))).thenReturn(false);
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(eq(failureAuthRecordedDTO))).thenReturn(true);
            mockedAuthCheck.when(() -> AuthCheckManager.isSuccess(eq(authRecordedDTO))).thenReturn(true);
            
            when(authDetailDataModifyService.addAuthorizationLog(any())).thenReturn(1);

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // 验证方法调用
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            verify(authCheckItemInspecProcessService, never()).processAuthCheck(any(), any());
        }
    }

    @Test
    @DisplayName("测试authCheck方法-授权检查返回异常")
    void testAuthCheck_AuthCheckException() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);
            mockedAuthCheck.when(() -> AuthCheckManager.isSuccess(any(AuthRecordedDTO.class))).thenReturn(true);

            createTestData();

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(authCheckManager.apqcCheck(any(), eq(false))).thenReturn(authRecordedDTO);
            when(authCheckItemInspecProcessService.processAuthCheck(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());

            // 验证不会执行后续流程
            verify(postAccountService, never()).postAccount(any());
        }
    }

    @Test
    @DisplayName("测试authCheck方法-包含分期信息")
    void testAuthCheck_WithInstallmentInfo() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);
            mockedAuthCheck.when(() -> AuthCheckManager.isSuccess(any(AuthRecordedDTO.class))).thenReturn(true);

            createTestData();

            // 设置分期信息
            InstallTrialResDTO installTrialResDTO = createInstallTrialResDTO();
            authorizationCheckProcessingPayload.setInstallTrialResDTO(installTrialResDTO);

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(authCheckManager.apqcCheck(any(), eq(false))).thenReturn(authRecordedDTO);
            when(authCheckItemInspecProcessService.processAuthCheck(any(), any())).thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            doNothing().when(authCheckManager).getArpcValue(any(), eq(true));
            when(postAccountService.postAccount(any())).thenReturn("ORDER789");
            doNothing().when(authSmsManager).sendMsg(any());
            doNothing().when(integralServiceManager).integralService(any());

            // Mock ApplicationManager
            ApplicationManager.applicationContext = applicationContext;
            when(applicationContext.getBean(eq("0200_Service"), eq(AbstractTransRoutingService.class)))
                .thenReturn(abstractTransRoutingService);

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getInstallmentTotalFee()).isEqualTo(new BigDecimal("1000"));
            assertThat(authRecordedDTO.getInstallmentFirstTermAmount()).isEqualTo(new BigDecimal("500"));
            assertThat(authRecordedDTO.getInstallmentFirstTermFee()).isEqualTo(new BigDecimal("50"));
            assertThat(authRecordedDTO.getInstallmentTermFee()).isEqualTo(new BigDecimal("25"));
        }
    }

    @Test
    @DisplayName("测试authCheck方法-空的交易类型使用默认值")
    void testAuthCheck_EmptyTransTypeUseDefault() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);

            createTestData();
            // 设置空的交易类型
            authRecordedDTO.setAuthTransType("");

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(postAccountService.postAccount(any())).thenReturn("ORDER999");
            doNothing().when(authSmsManager).sendMsg(any());
            doNothing().when(integralServiceManager).integralService(any());

            // Mock ApplicationManager - 使用默认的交易类型
            ApplicationManager.applicationContext = applicationContext;
            String defaultTransType = AuthMessageTypeIdEnum.DEFAULT_MESSAGE_TYPE.getTransType();
            when(applicationContext.getBean(eq(defaultTransType + "_Service"), eq(AbstractTransRoutingService.class)))
                .thenReturn(abstractTransRoutingService);

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 验证使用了默认的交易类型
            verify(applicationContext).getBean(eq(defaultTransType + "_Service"), eq(AbstractTransRoutingService.class));
        }
    }

    @Test
    @DisplayName("测试authCheck方法-null的交易类型使用默认值")
    void testAuthCheck_NullTransTypeUseDefault() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);

            createTestData();
            // 设置null的交易类型
            authRecordedDTO.setAuthTransType(null);

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(postAccountService.postAccount(any())).thenReturn("ORDER888");
            doNothing().when(authSmsManager).sendMsg(any());
            doNothing().when(integralServiceManager).integralService(any());

            // Mock ApplicationManager - 使用默认的交易类型
            ApplicationManager.applicationContext = applicationContext;
            String defaultTransType = AuthMessageTypeIdEnum.DEFAULT_MESSAGE_TYPE.getTransType();
            when(applicationContext.getBean(eq(defaultTransType + "_Service"), eq(AbstractTransRoutingService.class)))
                .thenReturn(abstractTransRoutingService);

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 验证使用了默认的交易类型
            verify(applicationContext).getBean(eq(defaultTransType + "_Service"), eq(AbstractTransRoutingService.class));
        }
    }

    @Test
    @DisplayName("测试authCheck方法-RuntimeException异常处理")
    void testAuthCheck_RuntimeExceptionHandling() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());

            createTestData();

            // Mock依赖方法抛出RuntimeException
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenThrow(new RuntimeException("Test Runtime Exception"));

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckProcessService.authCheck(authRecordedDTO))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Test Runtime Exception");

            // 验证finally块中的清理方法被调用
            mockedCustAccount.verify(() -> CustAccountBO.clearThreadLocalNotBatch());
        }
    }

    @Test
    @DisplayName("测试authCheck方法-分期信息为null")
    void testAuthCheck_NullInstallmentInfo() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);
            mockedAuthCheck.when(() -> AuthCheckManager.isSuccess(any(AuthRecordedDTO.class))).thenReturn(true);

            createTestData();

            // 确保分期信息为null
            authorizationCheckProcessingPayload.setInstallTrialResDTO(null);

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(authCheckManager.apqcCheck(any(), eq(false))).thenReturn(authRecordedDTO);
            when(authCheckItemInspecProcessService.processAuthCheck(any(), any())).thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            doNothing().when(authCheckManager).getArpcValue(any(), eq(true));
            when(postAccountService.postAccount(any())).thenReturn("ORDER111");
            doNothing().when(authSmsManager).sendMsg(any());
            doNothing().when(integralServiceManager).integralService(any());

            // Mock ApplicationManager
            ApplicationManager.applicationContext = applicationContext;
            when(applicationContext.getBean(eq("0200_Service"), eq(AbstractTransRoutingService.class)))
                .thenReturn(abstractTransRoutingService);

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            
            // 验证分期信息没有被设置
            assertThat(authRecordedDTO.getInstallmentTotalFee()).isNull();
            assertThat(authRecordedDTO.getInstallmentFirstTermAmount()).isNull();
            assertThat(authRecordedDTO.getInstallmentFirstTermFee()).isNull();
            assertThat(authRecordedDTO.getInstallmentTermFee()).isNull();
        }
    }

    @Test
    @DisplayName("测试authCheck方法-非普通交易类型跳过授权检查")
    void testAuthCheck_NonNormalTransactionType() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);

            createTestData();

            // 设置为非普通交易类型
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVERSAL_TRANS.getCode());

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(postAccountService.postAccount(any())).thenReturn("ORDER222");
            doNothing().when(authSmsManager).sendMsg(any());
            doNothing().when(integralServiceManager).integralService(any());

            // Mock ApplicationManager - 使用正确的交易类型
            ApplicationManager.applicationContext = applicationContext;
            when(applicationContext.getBean(anyString(), eq(AbstractTransRoutingService.class)))
                .thenReturn(abstractTransRoutingService);

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 验证授权检查相关方法没有被调用
            verify(authCheckItemInspecProcessService, never()).processAuthCheck(any(), any());
            verify(authCheckManager, never()).apqcCheck(any(), anyBoolean());
            verify(authCheckManager, never()).getArpcValue(any(), anyBoolean());
        }
    }

    @Test
    @DisplayName("测试authCheck方法-授权失败时跳过授权检查")
    void testAuthCheck_AuthFailureSkipsCheck() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);
            mockedAuthCheck.when(() -> AuthCheckManager.isSuccess(any(AuthRecordedDTO.class))).thenReturn(false);

            createTestData();

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(postAccountService.postAccount(any())).thenReturn("ORDER333");
            doNothing().when(authSmsManager).sendMsg(any());
            doNothing().when(integralServiceManager).integralService(any());

            // Mock ApplicationManager
            ApplicationManager.applicationContext = applicationContext;
            when(applicationContext.getBean(eq("0200_Service"), eq(AbstractTransRoutingService.class)))
                .thenReturn(abstractTransRoutingService);

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 验证授权检查相关方法没有被调用
            verify(authCheckItemInspecProcessService, never()).processAuthCheck(any(), any());
            verify(authCheckManager, never()).apqcCheck(any(), anyBoolean());
            verify(authCheckManager, never()).getArpcValue(any(), anyBoolean());
        }
    }

    @Test
    @DisplayName("测试authCheck方法-APQC检查失败返回拒绝码")
    void testAuthCheck_ApqcCheckFailureReturnsReject() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(true);
            mockedAuthCheck.when(() -> AuthCheckManager.isSuccess(any(AuthRecordedDTO.class))).thenReturn(true);

            createTestData();

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(authCheckManager.apqcCheck(any(), eq(false))).thenReturn(authRecordedDTO);
            // addAuthorizationLog方法不是void方法，不需要Mock

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // 验证授权日志被添加
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);

            // 验证后续检查没有被调用
            verify(authCheckItemInspecProcessService, never()).processAuthCheck(any(), any());
        }
    }

    @Test
    @DisplayName("测试authCheck方法-授权检查返回异常码")
    void testAuthCheck_AuthCheckReturnsException() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);
            mockedAuthCheck.when(() -> AuthCheckManager.isSuccess(any(AuthRecordedDTO.class))).thenReturn(true);

            createTestData();

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(authCheckManager.apqcCheck(any(), eq(false))).thenReturn(authRecordedDTO);
            when(authCheckItemInspecProcessService.processAuthCheck(any(), any())).thenReturn(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());
            doNothing().when(authCheckManager).getArpcValue(any(), eq(true));

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());

            // 验证授权检查被调用
            verify(authCheckItemInspecProcessService).processAuthCheck(authorizationCheckProcessingPayload, dataResponseFuture);
            verify(authCheckManager).getArpcValue(authorizationCheckProcessingPayload, true);
        }
    }

    @Test
    @DisplayName("测试authCheck方法-数据更新返回异常码")
    void testAuthCheck_DataUpdateReturnsException() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedOrgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> mockedThreadLocal = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> mockedAuthCheck = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> mockedCustAccount = mockStatic(CustAccountBO.class)) {

            // Mock静态方法
            mockedOrgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            mockedThreadLocal.when(AuthThreadLocalManager::getReqData).thenReturn(new HashMap<>());
            mockedAuthCheck.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(false);
            mockedAuthCheck.when(() -> AuthCheckManager.isSuccess(any(AuthRecordedDTO.class))).thenReturn(true);

            createTestData();

            // Mock依赖方法
            when(authCheckManager.checkAnyCloudFraud(any(), any(), any())).thenReturn(dataResponseFuture);
            when(authCheckDataPrepareService.prepareAuthData(any())).thenReturn(authorizationCheckProcessingPayload);
            when(authCheckManager.apqcCheck(any(), eq(false))).thenReturn(authRecordedDTO);
            when(authCheckItemInspecProcessService.processAuthCheck(any(), any())).thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            doNothing().when(authCheckManager).getArpcValue(any(), eq(true));

            // Mock ApplicationManager
            ApplicationManager.applicationContext = applicationContext;
            when(applicationContext.getBean(eq("0200_Service"), eq(AbstractTransRoutingService.class)))
                .thenReturn(abstractTransRoutingService);

            // 这里我们通过修改代码中的dataResCode来模拟异常情况
            // 由于代码中dataResCode被硬编码为0，我们需要通过其他方式测试这个分支

            // 执行方法
            int result = authCheckProcessService.authCheck(authRecordedDTO);

            // 验证结果 - 由于dataResCode被硬编码为0，这里应该返回APPROVE_CODE
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
        }
    }
}