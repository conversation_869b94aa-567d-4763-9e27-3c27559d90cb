package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.service.manager.AuthDataUpdateManager;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTrancactionStatusEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.parameter.base.common.service.system.ISystemTableService;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import org.mockito.MockedStatic;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;

/**
 * @description AuthDataUpdateServiceImpl的单元测试类
 * <AUTHOR>
 * @date 2025/01/08
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("授权数据更新服务测试")
class AuthDataUpdateServiceImplTest {

    @Mock
    private CheckAuthResponseCodeServiceImpl checkAuthResponseCodeService;

    @Mock
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    @Mock
    private ISystemTableService iSystemTableService;

    @Mock
    private AuthDataUpdateManager authDataUpdateManager;

    @InjectMocks
    private AuthDataUpdateServiceImpl authDataUpdateService;

    private AuthorizationCheckProcessingPayload payload;
    private AuthRecordedDTO authRecordedDTO;
    private CardAuthorizationDTO cardAuthorizationDTO;
    private SystemTableDTO systemTableDTO;
    private OrganizationInfoResDTO organizationInfoResDTO;

    @BeforeEach
    void setUp() {
        // 使用MockedStatic创建需要OrgNumberUtils的对象以避免初始化问题
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            payload = new AuthorizationCheckProcessingPayload();
            authRecordedDTO = new AuthRecordedDTO();
            cardAuthorizationDTO = new CardAuthorizationDTO();
            systemTableDTO = new SystemTableDTO();
            organizationInfoResDTO = new OrganizationInfoResDTO();
        }
        
        payload.setAuthRecordedDTO(authRecordedDTO);
        payload.setCardAuthorizationDTO(cardAuthorizationDTO);
        payload.setOrgInfo(organizationInfoResDTO);
        
        systemTableDTO.setAuthorizationLogFlag(AuthConstans.FLAG_YES);
    }

    @Test
    @DisplayName("成功场景 - 预授权交易授权成功")
    void testAuthDataUpdate_PreAuthSuccess() {
        // Arrange
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        authRecordedDTO.setPreAuth(true);
        
        when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
        when(checkAuthResponseCodeService.preAuthUpdate(any())).thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

        // Act
        int result = authDataUpdateService.authDataUpdate(payload);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        assertEquals(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
        verify(checkAuthResponseCodeService).preAuthUpdate(payload);
        verify(authDataUpdateManager, never()).updateAuthorization(any());
    }

    @Test
    @DisplayName("成功场景 - 预授权完成交易授权成功")
    void testAuthDataUpdate_PreAuthCompleteSuccess() {
        // Arrange
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        authRecordedDTO.setPreAuthComplete(true);
        
        when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
        when(checkAuthResponseCodeService.preAuthUpdate(any())).thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

        // Act
        int result = authDataUpdateService.authDataUpdate(payload);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        assertEquals(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
        verify(checkAuthResponseCodeService).preAuthUpdate(payload);
    }

    @Test
    @DisplayName("成功场景 - POS查询交易授权成功")
    void testAuthDataUpdate_PosQuerySuccess() {
        // Arrange
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        authRecordedDTO.setAuthTransactionTypeTopCode("Q");
        authRecordedDTO.setPreAuth(false);
        authRecordedDTO.setPreAuthComplete(false);
        
        when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);

        // Act
        int result = authDataUpdateService.authDataUpdate(payload);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        assertEquals(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
        verify(checkAuthResponseCodeService).authDataUpdateLogicD(payload);
    }

    @Test
    @DisplayName("成功场景 - 普通授权交易成功")
    void testAuthDataUpdate_NormalAuthSuccess() {
        // Arrange
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        authRecordedDTO.setAuthTransactionTypeTopCode("P");
        authRecordedDTO.setPreAuth(false);
        authRecordedDTO.setPreAuthComplete(false);
        
        when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
        when(checkAuthResponseCodeService.authDataUpdateLogicB(any())).thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

        // Act
        int result = authDataUpdateService.authDataUpdate(payload);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        verify(checkAuthResponseCodeService).authDataUpdateLogicB(payload);
    }

    @Test
    @DisplayName("失败场景 - 授权交易拒绝")
    void testAuthDataUpdate_AuthorizationRejected() {
        // Arrange
        authRecordedDTO.setAuthResponseCode("05"); // 拒绝码
        authRecordedDTO.setAuthTransactionTypeTopCode("P");
        authRecordedDTO.setPreAuth(false);
        authRecordedDTO.setPreAuthComplete(false);
        
        when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
        when(checkAuthResponseCodeService.authDataUpdateLogicC(any())).thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

        // Act
        int result = authDataUpdateService.authDataUpdate(payload);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.REJECT_CODE.getCode(), result);
        assertEquals(AuthTrancactionStatusEnum.ERROR_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
        verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        verify(authDataUpdateManager).updateAuthorization(payload);
        verify(checkAuthResponseCodeService).authDataUpdateLogicC(payload);
    }

    @Test
    @DisplayName("异常场景 - 系统配置获取失败")
    void testAuthDataUpdate_SystemConfigNotFound() {
        // Arrange
        when(iSystemTableService.findBySystemId("0000")).thenReturn(null);

        // Act & Assert
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
            authDataUpdateService.authDataUpdate(payload);
        });
        
        // 验证异常消息包含预期的错误信息
        assertTrue(exception.getMessage().contains("S_SELECT_SYSTEM_FAIL") ||
                  exception.getErrCode().equals(AnyTxnAuthRespCodeEnum.S_SELECT_SYSTEM_FAIL.getCode()));
    }

    @Test
    @DisplayName("异常场景 - 系统服务抛出异常")
    void testAuthDataUpdate_SystemServiceException() {
        // Arrange
        when(iSystemTableService.findBySystemId("0000")).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            authDataUpdateService.authDataUpdate(payload);
        });
    }

    @Test
    @DisplayName("边界场景 - 空的授权记录DTO")
    void testAuthDataUpdate_EmptyAuthRecordedDTO() {
        // Arrange
        payload.setAuthRecordedDTO(null);

        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            authDataUpdateService.authDataUpdate(payload);
        });
    }

    @Test
    @DisplayName("边界场景 - 空的卡授权DTO")
    void testAuthDataUpdate_EmptyCardAuthorizationDTO() {
        // Arrange
        payload.setCardAuthorizationDTO(null);
        authRecordedDTO.setAuthResponseCode("05");
        
        when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
        when(checkAuthResponseCodeService.authDataUpdateLogicC(any())).thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

        // Act
        int result = authDataUpdateService.authDataUpdate(payload);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.REJECT_CODE.getCode(), result);
        verify(authDataUpdateManager).updateAuthorization(payload);
    }

    @Test
    @DisplayName("边界场景 - 空的机构信息DTO")
    void testAuthDataUpdate_EmptyOrganizationInfoResDTO() {
        // Arrange
        payload.setOrgInfo(null);
        authRecordedDTO.setAuthResponseCode("05");
        
        when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
        when(checkAuthResponseCodeService.authDataUpdateLogicC(any())).thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

        // Act
        int result = authDataUpdateService.authDataUpdate(payload);

        // Assert
        assertEquals(AuthItemCheckResCodeEnum.REJECT_CODE.getCode(), result);
        verify(authDataUpdateManager).updateAuthorization(payload);
    }

    @Test
    @DisplayName("测试系统配置获取成功")
    void testGetSystemConfig_Success() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);

            // Act - 通过调用authDataUpdate间接测试getSystemConfig
            authRecordedDTO.setAuthResponseCode("05"); // 拒绝交易
            when(checkAuthResponseCodeService.authDataUpdateLogicC(any())).thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            int result = authDataUpdateService.authDataUpdate(payload);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.REJECT_CODE.getCode(), result);
            verify(iSystemTableService).findBySystemId("0000");
        }
    }

    @Test
    @DisplayName("测试不同授权响应码的处理")
    void testAuthDataUpdate_DifferentResponseCodes() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO.setAuthResponseCode("14"); // 无效卡号
            when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
            when(checkAuthResponseCodeService.authDataUpdateLogicC(any())).thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // Act
            int result = authDataUpdateService.authDataUpdate(payload);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.REJECT_CODE.getCode(), result);
            verify(authDataUpdateManager).updateAuthorization(payload);
            verify(checkAuthResponseCodeService).authDataUpdateLogicC(payload);
        }
    }

    @Test
    @DisplayName("测试授权日志标识开启的拒绝交易")
    void testAuthDataUpdate_RejectWithAuthLogEnabled() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO.setAuthResponseCode("05");
            systemTableDTO.setAuthorizationLogFlag(AuthConstans.FLAG_YES);

            when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
            when(authDetailDataModifyService.addAuthorizationLog(any())).thenReturn(1);
            when(checkAuthResponseCodeService.authDataUpdateLogicC(any())).thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // Act
            int result = authDataUpdateService.authDataUpdate(payload);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.REJECT_CODE.getCode(), result);
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            verify(authDataUpdateManager).updateAuthorization(payload);
            verify(checkAuthResponseCodeService).authDataUpdateLogicC(payload);
        }
    }

    @Test
    @DisplayName("测试授权日志标识关闭的拒绝交易")
    void testAuthDataUpdate_RejectWithAuthLogDisabled() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO.setAuthResponseCode("05");
            systemTableDTO.setAuthorizationLogFlag("N");

            when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
            when(checkAuthResponseCodeService.authDataUpdateLogicC(any())).thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // Act
            int result = authDataUpdateService.authDataUpdate(payload);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.REJECT_CODE.getCode(), result);
            verify(authDetailDataModifyService, never()).addAuthorizationLog(any());
            verify(authDataUpdateManager).updateAuthorization(payload);
            verify(checkAuthResponseCodeService).authDataUpdateLogicC(payload);
        }
    }

    @Test
    @DisplayName("测试事务回滚场景")
    void testAuthDataUpdate_TransactionRollback() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO.setAuthResponseCode("05");
            when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
            when(checkAuthResponseCodeService.authDataUpdateLogicC(any())).thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // Act
            int result = authDataUpdateService.authDataUpdate(payload);

            // Assert
            assertEquals(AuthItemCheckResCodeEnum.REJECT_CODE.getCode(), result);
            verify(authDataUpdateManager).updateAuthorization(payload);
        }
    }

    @Test
    @DisplayName("测试authDataUpdateLogicB方法抛出异常")
    void testAuthDataUpdate_AuthDataUpdateLogicBException() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
            authRecordedDTO.setAuthTransactionTypeTopCode("P");
            authRecordedDTO.setPreAuth(false);
            authRecordedDTO.setPreAuthComplete(false);

            when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
            when(checkAuthResponseCodeService.authDataUpdateLogicB(any())).thenThrow(new RuntimeException("数据更新异常"));

            // Act & Assert
            assertThrows(RuntimeException.class, () -> {
                authDataUpdateService.authDataUpdate(payload);
            });

            verify(checkAuthResponseCodeService).authDataUpdateLogicB(payload);
        }
    }

    @Test
    @DisplayName("测试authDataUpdateLogicC方法抛出异常")
    void testAuthDataUpdate_AuthDataUpdateLogicCException() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            authRecordedDTO.setAuthResponseCode("05");
            systemTableDTO.setAuthorizationLogFlag("N");

            when(iSystemTableService.findBySystemId("0000")).thenReturn(systemTableDTO);
            when(checkAuthResponseCodeService.authDataUpdateLogicC(any())).thenThrow(new RuntimeException("拒绝数据更新异常"));

            // Act & Assert
            assertThrows(RuntimeException.class, () -> {
                authDataUpdateService.authDataUpdate(payload);
            });

            verify(authDataUpdateManager).updateAuthorization(payload);
            verify(checkAuthResponseCodeService).authDataUpdateLogicC(payload);
        }
    }
}