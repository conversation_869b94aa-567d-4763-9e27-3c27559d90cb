/**
 * @description AuthAutoInstallServiceImpl的单元测试类
 * <AUTHOR>
 * @date 2025/07/08
 * @version 1.0
 * @AI 已复核
 */
package com.anytech.anytxn.authorization.service.auth;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.constants.InstallmentPriceFlagEnum;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthRepDetailEnum;
import com.anytech.anytxn.business.dao.installment.mapper.InstallAutoSignSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallAutoSign;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.matcher.RuleMatcherManager;
import com.anytech.anytxn.common.rule.matcher.TxnRuleMatcher;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallAccountingTransParm;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeParm;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMcc;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportTxn;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.installment.mapper.InstallAccountingTransParmSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeParmSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportMccSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportTxnSelfMapper;

@ExtendWith(MockitoExtension.class)
@DisplayName("AuthAutoInstallServiceImpl单元测试")
class AuthAutoInstallServiceImplTest {

    @Mock
    private InstallAutoSignSelfMapper installAutoSignSelfMapper;

    @Mock
    private IInstallProductInfoService installProductInfoService;

    @Mock
    private InstallTypeSupportMccSelfMapper installTypeSupportMccSelfMapper;

    @Mock
    private InstallTypeSupportTxnSelfMapper installTypeSupportTxnSelfMapper;

    @Mock
    private IOrganizationInfoService organizationInfoService;

    @Mock
    private InstallTypeParmSelfMapper installTypeParmSelfMapper;

    @Mock
    private InstallAccountingTransParmSelfMapper installAccountingTransParmSelfMapper;

    @InjectMocks
    private AuthAutoInstallServiceImpl authAutoInstallService;

    private AuthRecordedDTO authRecordedDTO;

    @BeforeEach
    void setUp() {
        // 在每个测试方法中创建测试数据，避免OrgNumberUtils问题
    }

    /**
     * 创建测试数据的帮助方法
     */
    private void createTestData() {
        authRecordedDTO = new AuthRecordedDTO();
        authRecordedDTO.setAuthCardNumber("****************");
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("1000.00"));
        authRecordedDTO.setAuthMerchantCountryCode("5411");
        authRecordedDTO.setPostingTransactionCode("00");
        authRecordedDTO.setAuthTransactionCurrencyCode("156");
        authRecordedDTO.setMerchantId("MERCHANT001");
        authRecordedDTO.setAuthMerchantType("5411");
        authRecordedDTO.setAuthLocalTransactionDate("1208");
        authRecordedDTO.setAuthLocalTransactionTime("143000");
    }

    @Test
    @DisplayName("测试autoInstall方法-成功自动分期")
    void testAutoInstall_Success() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 准备测试数据
            String orgNumber = "001";
            String installType = "LD";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            
            Map<String, Object> ruleResult = new HashMap<>();
            ruleResult.put("installProductCode", installProductCode);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(ruleResult);

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 产品信息
            InstallProductInfoResDTO productInfo = new InstallProductInfoResDTO();
            productInfo.setProdType(installType);
            productInfo.setPostingTransactionParmId("PARM001");
            productInfo.setTerm(12);
            when(installProductInfoService.findByIndex(orgNumber, installProductCode)).thenReturn(productInfo);

            // Mock 分期类型参数
            InstallTypeParm installTypeParm = new InstallTypeParm();
            installTypeParm.setAuthTransactionType("01");
            installTypeParm.setAuthTransactionTypeDetail("001");
            when(installTypeParmSelfMapper.selectByIndex(orgNumber, installType)).thenReturn(installTypeParm);

            // Mock 会计交易参数
            InstallAccountingTransParm accountingTransParm = new InstallAccountingTransParm();
            accountingTransParm.setInstallTransactionCode("10");
            accountingTransParm.setInstallReturnCodeRev("11");
            when(installAccountingTransParmSelfMapper.selectByIndex(orgNumber, "PARM001")).thenReturn(accountingTransParm);

            // Mock 交易码支持
            List<InstallTypeSupportTxn> supportTxns = Arrays.asList(createInstallTypeSupportTxn("00"));
            when(installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(installType, orgNumber)).thenReturn(supportTxns);

            // Mock MCC不在禁止列表中
            when(installTypeSupportMccSelfMapper.selectByOrgTypeAndMcc(orgNumber, installType, "5411")).thenReturn(null);

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证结果
            assertThat(authRecordedDTO.getAuthTransactionTypeTopCode()).isEqualTo("01");
            assertThat(authRecordedDTO.getAuthTransactionTypeDetailCode()).isEqualTo("001");
            assertThat(authRecordedDTO.getPostingTransactionCode()).isEqualTo("10");
            assertThat(authRecordedDTO.getPostingTransactionCodeRev()).isEqualTo("11");
            assertThat(authRecordedDTO.getInstallmentFeeRate()).isEqualTo(BigDecimal.ZERO);
            assertThat(authRecordedDTO.getInstallmentPriceFlag()).isEqualTo(InstallmentPriceFlagEnum.BASICPRICING_FLAG.getCode());
            assertThat(authRecordedDTO.getInstallmentDerateMethod()).isEqualTo(String.valueOf(AuthConstans.ZERO));
            assertThat(authRecordedDTO.getTerm()).isEqualTo(12);
            assertThat(authRecordedDTO.getInstallmentProductCode()).isEqualTo(installProductCode);

            // 验证方法调用
            verify(installProductInfoService, times(2)).findByIndex(orgNumber, installProductCode);
            verify(installTypeParmSelfMapper).selectByIndex(orgNumber, installType);
            verify(installAccountingTransParmSelfMapper).selectByIndex(orgNumber, "PARM001");
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-不符合自动分期条件")
    void testAutoInstall_NotAutoInstall() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";

            // Mock 规则引擎返回空
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(new HashMap<>());

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 没有自动分期协议
            when(installAutoSignSelfMapper.selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber()))
                .thenReturn(new ArrayList<>());

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用分期相关的设置方法
            verify(installTypeParmSelfMapper, never()).selectByIndex(anyString(), anyString());
            verify(installAccountingTransParmSelfMapper, never()).selectByIndex(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-分期类型参数不存在")
    void testAutoInstall_InstallTypeParamNotExists() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installType = "LD";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            
            Map<String, Object> ruleResult = new HashMap<>();
            ruleResult.put("installProductCode", installProductCode);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(ruleResult);

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 产品信息
            InstallProductInfoResDTO productInfo = new InstallProductInfoResDTO();
            productInfo.setProdType(installType);
            when(installProductInfoService.findByIndex(orgNumber, installProductCode)).thenReturn(productInfo);

            // Mock 分期类型参数不存在
            when(installTypeParmSelfMapper.selectByIndex(orgNumber, installType)).thenReturn(null);

            // Mock 交易码支持
            List<InstallTypeSupportTxn> supportTxns = Arrays.asList(createInstallTypeSupportTxn("00"));
            when(installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(installType, orgNumber)).thenReturn(supportTxns);

            // Mock MCC不在禁止列表中
            when(installTypeSupportMccSelfMapper.selectByOrgTypeAndMcc(orgNumber, installType, "5411")).thenReturn(null);

            // 执行方法并验证异常
            assertThatThrownBy(() -> authAutoInstallService.autoInstall(authRecordedDTO))
                .isInstanceOf(AnyTxnAuthException.class);

            // 验证方法调用
            verify(installTypeParmSelfMapper).selectByIndex(orgNumber, installType);
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-通过LD协议自动分期")
    void testAutoInstall_ByLDProtocol() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installType = "LD";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回空
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(new HashMap<>());

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock LD自动分期协议
            List<InstallAutoSign> autoSigns = Arrays.asList(createInstallAutoSign("LD", installProductCode, "156", new BigDecimal("500.00")));
            when(installAutoSignSelfMapper.selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber()))
                .thenReturn(autoSigns);

            // Mock 分期类型参数
            InstallTypeParm installTypeParm = new InstallTypeParm();
            installTypeParm.setAuthTransactionType("01");
            installTypeParm.setAuthTransactionTypeDetail("001");
            when(installTypeParmSelfMapper.selectByIndex(orgNumber, installType)).thenReturn(installTypeParm);

            // Mock 产品信息
            InstallProductInfoResDTO productInfo = new InstallProductInfoResDTO();
            productInfo.setPostingTransactionParmId("PARM001");
            productInfo.setTerm(12);
            when(installProductInfoService.findByIndex(orgNumber, installProductCode)).thenReturn(productInfo);

            // Mock 会计交易参数
            InstallAccountingTransParm accountingTransParm = new InstallAccountingTransParm();
            accountingTransParm.setInstallTransactionCode("10");
            accountingTransParm.setInstallReturnCodeRev("11");
            when(installAccountingTransParmSelfMapper.selectByIndex(orgNumber, "PARM001")).thenReturn(accountingTransParm);

            // Mock 交易码支持
            List<InstallTypeSupportTxn> supportTxns = Arrays.asList(createInstallTypeSupportTxn("00"));
            when(installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(installType, orgNumber)).thenReturn(supportTxns);

            // Mock MCC不在禁止列表中
            when(installTypeSupportMccSelfMapper.selectByOrgTypeAndMcc(orgNumber, installType, "5411")).thenReturn(null);

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证结果
            assertThat(authRecordedDTO.getInstallmentProductCode()).isEqualTo(installProductCode);
            assertThat(authRecordedDTO.getTerm()).isEqualTo(12);

            // 验证方法调用
            verify(installAutoSignSelfMapper).selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber());
            verify(installTypeSupportTxnSelfMapper).selectByTypeAndOrgNum(installType, orgNumber);
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-通过RD协议自动分期")
    void testAutoInstall_ByRDProtocol() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installType = "RD";
            String installProductCode = "INSTALL002";

            // Mock 规则引擎返回空
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(new HashMap<>());

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock RD自动分期协议（LD不满足条件，RD满足）
            List<InstallAutoSign> autoSigns = Arrays.asList(
                createInstallAutoSign("LD", installProductCode, "156", new BigDecimal("2000.00")), // 金额不满足
                createInstallAutoSign("RD", installProductCode, "156", new BigDecimal("500.00"))   // 金额满足
            );
            when(installAutoSignSelfMapper.selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber()))
                .thenReturn(autoSigns);

            // Mock 分期类型参数
            InstallTypeParm installTypeParm = new InstallTypeParm();
            installTypeParm.setAuthTransactionType("02");
            installTypeParm.setAuthTransactionTypeDetail("002");
            when(installTypeParmSelfMapper.selectByIndex(orgNumber, installType)).thenReturn(installTypeParm);

            // Mock 产品信息
            InstallProductInfoResDTO productInfo = new InstallProductInfoResDTO();
            productInfo.setPostingTransactionParmId("PARM002");
            productInfo.setTerm(6);
            when(installProductInfoService.findByIndex(orgNumber, installProductCode)).thenReturn(productInfo);

            // Mock 会计交易参数
            InstallAccountingTransParm accountingTransParm = new InstallAccountingTransParm();
            accountingTransParm.setInstallTransactionCode("20");
            accountingTransParm.setInstallReturnCodeRev("21");
            when(installAccountingTransParmSelfMapper.selectByIndex(orgNumber, "PARM002")).thenReturn(accountingTransParm);

            // Mock 交易码支持
            List<InstallTypeSupportTxn> supportTxns = Arrays.asList(createInstallTypeSupportTxn("00"));
            when(installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(installType, orgNumber)).thenReturn(supportTxns);

            // Mock MCC不在禁止列表中
            when(installTypeSupportMccSelfMapper.selectByOrgTypeAndMcc(orgNumber, installType, "5411")).thenReturn(null);

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证结果
            assertThat(authRecordedDTO.getAuthTransactionTypeTopCode()).isEqualTo("02");
            assertThat(authRecordedDTO.getAuthTransactionTypeDetailCode()).isEqualTo("002");
            assertThat(authRecordedDTO.getInstallmentProductCode()).isEqualTo(installProductCode);
            assertThat(authRecordedDTO.getTerm()).isEqualTo(6);

            // 验证方法调用
            verify(installAutoSignSelfMapper).selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-交易码不支持分期")
    void testAutoInstall_TransactionCodeNotSupported() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installType = "LD";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            
            Map<String, Object> ruleResult = new HashMap<>();
            ruleResult.put("installProductCode", installProductCode);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(ruleResult);

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 产品信息
            InstallProductInfoResDTO productInfo = new InstallProductInfoResDTO();
            productInfo.setProdType(installType);
            when(installProductInfoService.findByIndex(orgNumber, installProductCode)).thenReturn(productInfo);

            // Mock 交易码不支持（返回不包含当前交易码的列表）
            List<InstallTypeSupportTxn> supportTxns = Arrays.asList(createInstallTypeSupportTxn("01"));
            when(installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(installType, orgNumber)).thenReturn(supportTxns);

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用分期相关的设置方法
            verify(installTypeParmSelfMapper, never()).selectByIndex(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-MCC不支持分期")
    void testAutoInstall_MccNotSupported() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installType = "LD";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            
            Map<String, Object> ruleResult = new HashMap<>();
            ruleResult.put("installProductCode", installProductCode);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(ruleResult);

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 产品信息
            InstallProductInfoResDTO productInfo = new InstallProductInfoResDTO();
            productInfo.setProdType(installType);
            when(installProductInfoService.findByIndex(orgNumber, installProductCode)).thenReturn(productInfo);

            // Mock 交易码支持
            List<InstallTypeSupportTxn> supportTxns = Arrays.asList(createInstallTypeSupportTxn("00"));
            when(installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(installType, orgNumber)).thenReturn(supportTxns);

            // Mock MCC在禁止列表中
            InstallTypeSupportMcc supportMcc = new InstallTypeSupportMcc();
            when(installTypeSupportMccSelfMapper.selectByOrgTypeAndMcc(orgNumber, installType, "5411")).thenReturn(supportMcc);

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用分期相关的设置方法
            verify(installTypeParmSelfMapper, never()).selectByIndex(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-规则引擎为空")
    void testAutoInstall_RuleEngineNull() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";

            // Mock 规则引擎为空
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(null);

            // Mock 没有自动分期协议
            when(installAutoSignSelfMapper.selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber()))
                .thenReturn(new ArrayList<>());

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用分期相关的设置方法
            verify(installTypeParmSelfMapper, never()).selectByIndex(anyString(), anyString());
            verify(installAutoSignSelfMapper).selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-交易码支持列表为空")
    void testAutoInstall_SupportTxnListEmpty() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installType = "LD";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            
            Map<String, Object> ruleResult = new HashMap<>();
            ruleResult.put("installProductCode", installProductCode);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(ruleResult);

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 产品信息
            InstallProductInfoResDTO productInfo = new InstallProductInfoResDTO();
            productInfo.setProdType(installType);
            when(installProductInfoService.findByIndex(orgNumber, installProductCode)).thenReturn(productInfo);

            // Mock 交易码支持列表为空
            when(installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(installType, orgNumber)).thenReturn(null);

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用分期相关的设置方法
            verify(installTypeParmSelfMapper, never()).selectByIndex(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-空参数")
    void testAutoInstall_NullParameter() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");

            // 执行方法并验证异常
            assertThatThrownBy(() -> authAutoInstallService.autoInstall(null))
                .isInstanceOf(Exception.class);
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-协议金额不满足")
    void testAutoInstall_ProtocolAmountNotMet() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回空
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(new HashMap<>());

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 自动分期协议（基础金额大于交易金额）
            List<InstallAutoSign> autoSigns = Arrays.asList(
                createInstallAutoSign("LD", installProductCode, "156", new BigDecimal("2000.00")), // 金额不满足
                createInstallAutoSign("RD", installProductCode, "156", new BigDecimal("1500.00"))  // 金额也不满足
            );
            when(installAutoSignSelfMapper.selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber()))
                .thenReturn(autoSigns);

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用分期相关的设置方法
            verify(installTypeParmSelfMapper, never()).selectByIndex(anyString(), anyString());
            verify(installAutoSignSelfMapper).selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-币种不匹配")
    void testAutoInstall_CurrencyNotMatch() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回空
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(new HashMap<>());

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 自动分期协议（币种不匹配）
            List<InstallAutoSign> autoSigns = Arrays.asList(
                createInstallAutoSign("LD", installProductCode, "840", new BigDecimal("500.00")) // USD，不匹配CNY
            );
            when(installAutoSignSelfMapper.selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber()))
                .thenReturn(autoSigns);

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用分期相关的设置方法
            verify(installTypeParmSelfMapper, never()).selectByIndex(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-协议状态无效")
    void testAutoInstall_ProtocolStatusInvalid() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {
            
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回空
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(new HashMap<>());

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 自动分期协议（状态无效）
            InstallAutoSign autoSign = createInstallAutoSign("LD", installProductCode, "156", new BigDecimal("500.00"));
            autoSign.setStatus("0"); // 状态无效
            List<InstallAutoSign> autoSigns = Arrays.asList(autoSign);
            when(installAutoSignSelfMapper.selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber()))
                .thenReturn(autoSigns);

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用分期相关的设置方法
            verify(installTypeParmSelfMapper, never()).selectByIndex(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-InstallTypeParm为空不进行分期")
    void testAutoInstall_InstallTypeParmNull_NoInstallment() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {

            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installType = "LD";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回产品代码
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            Map<String, Object> ruleResult = new HashMap<>();
            ruleResult.put("result", installProductCode);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(ruleResult);

            // Mock 产品信息
            InstallProductInfoResDTO productInfo = new InstallProductInfoResDTO();
            productInfo.setProdType(installType);
            lenient().when(installProductInfoService.findByIndex(orgNumber, installProductCode)).thenReturn(productInfo);

            // Mock InstallTypeParm为空
            lenient().when(installTypeParmSelfMapper.selectByIndex(orgNumber, installType)).thenReturn(null);

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            lenient().when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // 执行方法 - 应该正常执行不抛异常
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用后续的分期设置方法
            verify(installAccountingTransParmSelfMapper, never()).selectByIndex(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-InstallAccountingTransParm为空不进行分期")
    void testAutoInstall_InstallAccountingTransParmNull_NoInstallment() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {

            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installType = "LD";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回产品代码
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            Map<String, Object> ruleResult = new HashMap<>();
            ruleResult.put("result", installProductCode);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(ruleResult);

            // Mock 产品信息
            InstallProductInfoResDTO productInfo = new InstallProductInfoResDTO();
            productInfo.setProdType(installType);
            lenient().when(installProductInfoService.findByIndex(orgNumber, installProductCode)).thenReturn(productInfo);

            // Mock InstallTypeParm
            InstallTypeParm installTypeParm = new InstallTypeParm();
            installTypeParm.setAuthTransactionType("01");
            installTypeParm.setAuthTransactionTypeDetail("001");
            lenient().when(installTypeParmSelfMapper.selectByIndex(orgNumber, installType)).thenReturn(installTypeParm);

            // Mock InstallAccountingTransParm为空
            lenient().when(installAccountingTransParmSelfMapper.selectByIndex(orgNumber, installType)).thenReturn(null);

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            lenient().when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // 执行方法 - 应该正常执行不抛异常
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证调用了产品信息查询
            verify(installProductInfoService).findByIndex(orgNumber, installProductCode);
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-机构信息为空会抛出异常")
    void testAutoInstall_OrganizationInfoNull() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {

            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            String orgNumber = "001";
            String installProductCode = "INSTALL001";

            // Mock 规则引擎返回产品代码
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            Map<String, Object> ruleResult = new HashMap<>();
            ruleResult.put("result", installProductCode);
            lenient().when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(ruleResult);

            // Mock 机构信息为空
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(null);

            // 执行方法并验证会抛出空指针异常
            assertThatThrownBy(() -> authAutoInstallService.autoInstall(authRecordedDTO))
                .isInstanceOf(NullPointerException.class);
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-交易金额为空")
    void testAutoInstall_TransactionAmountNull() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {

            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            authRecordedDTO.setAuthTransactionAmount(null); // 设置交易金额为空

            String orgNumber = "001";

            // Mock 规则引擎返回空
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(new HashMap<>());

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 没有自动分期协议
            when(installAutoSignSelfMapper.selectByOrgAndCardNum(orgNumber, authRecordedDTO.getAuthCardNumber()))
                .thenReturn(new ArrayList<>());

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用分期相关的设置方法
            verify(installTypeParmSelfMapper, never()).selectByIndex(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试autoInstall方法-卡号为空")
    void testAutoInstall_CardNumberNull() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class);
             MockedStatic<RuleMatcherManager> ruleManagerMock = mockStatic(RuleMatcherManager.class)) {

            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            authRecordedDTO.setAuthCardNumber(null); // 设置卡号为空

            String orgNumber = "001";

            // Mock 规则引擎返回空
            TxnRuleMatcher ruleMatcher = mock(TxnRuleMatcher.class);
            ruleManagerMock.when(() -> RuleMatcherManager.getMatcher("auto_install_rule", orgNumber))
                .thenReturn(ruleMatcher);
            when(ruleMatcher.execute(any(DataInputDTO.class))).thenReturn(new HashMap<>());

            // Mock 机构信息
            OrganizationInfoResDTO orgInfo = new OrganizationInfoResDTO();
            orgInfo.setNextProcessingDay(LocalDate.now());
            orgInfo.setToday(LocalDate.now());
            when(organizationInfoService.findOrganizationInfo(orgNumber)).thenReturn(orgInfo);

            // Mock 没有自动分期协议（因为卡号为空）
            when(installAutoSignSelfMapper.selectByOrgAndCardNum(orgNumber, null))
                .thenReturn(new ArrayList<>());

            // 执行方法
            authAutoInstallService.autoInstall(authRecordedDTO);

            // 验证没有调用分期相关的设置方法
            verify(installTypeParmSelfMapper, never()).selectByIndex(anyString(), anyString());
        }
    }

    /**
     * 创建InstallAutoSign测试对象
     */
    private InstallAutoSign createInstallAutoSign(String autoSignType, String installProductCode, String currencyCode, BigDecimal baseAmount) {
        InstallAutoSign autoSign = new InstallAutoSign();
        autoSign.setAutoSignType(autoSignType);
        autoSign.setInstallProductCode(installProductCode);
        autoSign.setCurrencyCode(currencyCode);
        autoSign.setBaseAmount(baseAmount);
        autoSign.setStatus("1");
        return autoSign;
    }

    /**
     * 创建InstallTypeSupportTxn测试对象
     */
    private InstallTypeSupportTxn createInstallTypeSupportTxn(String transactionCode) {
        InstallTypeSupportTxn supportTxn = new InstallTypeSupportTxn();
        supportTxn.setTransactionCode(transactionCode);
        return supportTxn;
    }
} 