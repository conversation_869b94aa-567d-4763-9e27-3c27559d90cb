package com.anytech.anytxn.authorization.service.manager;

import com.anytech.anytxn.authorization.base.service.auth.ICurrencyCommonService;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.CommonAccountDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.account.service.CommonAccountService;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthRepDetailEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;

import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AuthCheckItemManager单元测试
 * <AUTHOR>
 * @date 2025/07/15
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthCheckItemManager单元测试")
class AuthCheckItemManagerTest {

    @Mock
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Mock
    private ICurrencyCommonService currencyCommonService;

    @Mock
    private CommonAccountService commonAccountService;

    @InjectMocks
    private AuthCheckItemManager authCheckItemManager;

    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;
    private AuthRecordedDTO authRecordedDTO;
    private CardAuthorizationDTO cardAuthorizationDTO;
    private AuthRecordedDTO.AccountGroupAuthControlDTO primaryAccountGroupAuthControlDTO;

    @BeforeEach
    void setUp() {
        // 测试数据将在各个测试方法中初始化，以避免OrgNumberUtils的null问题
    }

    private AuthRecordedDTO createTestAuthRecordedDTO() {
        AuthRecordedDTO dto = new AuthRecordedDTO();
        dto.setAuthCustomerId("*********");
        dto.setAuthCardNumber("****************");
        dto.setAuthTransactionCurrencyCode("840");
        return dto;
    }

    private void initializeTestData() {
        authRecordedDTO = createTestAuthRecordedDTO();

        cardAuthorizationDTO = new CardAuthorizationDTO();
        cardAuthorizationDTO.setOpenDate(LocalDate.of(2023, 1, 1));

        primaryAccountGroupAuthControlDTO = new AuthRecordedDTO.AccountGroupAuthControlDTO();
        primaryAccountGroupAuthControlDTO.setAcctProductCode("PROD001");

        authorizationCheckProcessingPayload = new AuthorizationCheckProcessingPayload();
        authorizationCheckProcessingPayload.setAuthRecordedDTO(authRecordedDTO);
        authorizationCheckProcessingPayload.setCardAuthorizationDTO(cardAuthorizationDTO);
        authorizationCheckProcessingPayload.setAccountManagementInfoDTO(new AccountManagementInfoDTO());
    }

    @Test
    @DisplayName("获取账户管理信息 - 主账产品组为空时构建虚拟账户")
    void getAccountManagementInfoDTO_WithNullPrimaryAccountGroup_ShouldBuildVirtualAccount() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            authRecordedDTO.setAccountGroupAuthControlList(null);

            // Act
            AccountManagementInfoDTO result = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);

            // Assert
            assertNotNull(result);
            assertEquals(LocalDate.of(2023, 1, 1), result.getOpenDate());
            assertEquals(LocalDate.of(2023, 1, 1), result.getLastStatementDate());
        }
    }

    @Test
    @DisplayName("获取账户管理信息 - 通过客户号和产品编号获取成功")
    void getAccountManagementInfoDTO_WithValidCustomerAndProduct_ShouldReturnAccountInfo() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            authRecordedDTO.setAccountGroupAuthControlList(java.util.Arrays.asList(primaryAccountGroupAuthControlDTO));

            AccountManagementInfo accountManagementInfo = new AccountManagementInfo();
            accountManagementInfo.setCustomerId("*********");
            accountManagementInfo.setProductNumber("PROD001");
            accountManagementInfo.setOpenDate(LocalDate.of(2023, 1, 1));

            when(commonAccountService.selectByCusIdProNumAndCurr(any(CommonAccountDTO.class)))
                .thenReturn(accountManagementInfo);

            // Act
            AccountManagementInfoDTO result = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);

            // Assert
            assertNotNull(result);
            assertEquals("*********", result.getCustomerId());
            assertEquals("PROD001", result.getProductNumber());
            verify(commonAccountService).selectByCusIdProNumAndCurr(any(CommonAccountDTO.class));
        }
    }

    @Test
    @DisplayName("获取账户管理信息 - 未找到账户信息时构建开户日期")
    void getAccountManagementInfoDTO_WithNoAccountFound_ShouldBuildOpenDate() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            authRecordedDTO.setAccountGroupAuthControlList(java.util.Arrays.asList(primaryAccountGroupAuthControlDTO));

            when(commonAccountService.selectByCusIdProNumAndCurr(any(CommonAccountDTO.class)))
                .thenReturn(null);

            // Act
            AccountManagementInfoDTO result = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);

            // Assert
            assertNotNull(result);
            assertEquals(LocalDate.of(2023, 1, 1), result.getOpenDate());
            assertEquals(LocalDate.of(2023, 1, 1), result.getLastStatementDate());
            verify(commonAccountService).selectByCusIdProNumAndCurr(any(CommonAccountDTO.class));
        }
    }

    @Test
    @DisplayName("汇率转换 - 正常转换成功")
    void converterRate_WithValidParameters_ShouldReturnConvertedAmount() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            String organizationNumber = "001";
            String sourceCurrencyCode = "840";
            String targetCurrencyCode = "156";
            BigDecimal amount = new BigDecimal("100.00");
            BigDecimal expectedResult = new BigDecimal("650.00");

            when(currencyCommonService.getCurrencyRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount))
                .thenReturn(expectedResult);

            // Act
            BigDecimal result = authCheckItemManager.converterRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount);

            // Assert
            assertEquals(expectedResult, result);
            verify(currencyCommonService).getCurrencyRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount);
        }
    }

    @Test
    @DisplayName("汇率转换 - 机构号为空时抛出异常")
    void converterRate_WithNullOrganizationNumber_ShouldThrowException() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            String sourceCurrencyCode = "840";
            String targetCurrencyCode = "156";
            BigDecimal amount = new BigDecimal("100.00");

            // Act & Assert
            AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
                authCheckItemManager.converterRate(null, sourceCurrencyCode, targetCurrencyCode, amount);
            });

            assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL.getCode(), exception.getErrCode());
        }
    }

    @Test
    @DisplayName("汇率转换 - 来源币种为空时抛出异常")
    void converterRate_WithBlankSourceCurrency_ShouldThrowException() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            String organizationNumber = "001";
            String targetCurrencyCode = "156";
            BigDecimal amount = new BigDecimal("100.00");

            // Act & Assert
            AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
                authCheckItemManager.converterRate(organizationNumber, "", targetCurrencyCode, amount);
            });

            assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL.getCode(), exception.getErrCode());
        }
    }

    @Test
    @DisplayName("汇率转换 - 目标币种为空时抛出异常")
    void converterRate_WithBlankTargetCurrency_ShouldThrowException() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            String organizationNumber = "001";
            String sourceCurrencyCode = "840";
            BigDecimal amount = new BigDecimal("100.00");

            // Act & Assert
            AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
                authCheckItemManager.converterRate(organizationNumber, sourceCurrencyCode, null, amount);
            });

            assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL.getCode(), exception.getErrCode());
        }
    }

    @Test
    @DisplayName("验证AuthCheckItemManager是Spring服务组件")
    void testIsSpringService() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Assert - 验证类上有@Service注解
            assertTrue(AuthCheckItemManager.class.isAnnotationPresent(org.springframework.stereotype.Service.class));
        }
    }

    @Test
    @DisplayName("验证AuthCheckItemManager类存在")
    void testAuthCheckItemManagerClassExists() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // Assert
            assertNotNull(AuthCheckItemManager.class);
            assertEquals("AuthCheckItemManager", AuthCheckItemManager.class.getSimpleName());
        }
    }

    @Test
    @DisplayName("获取账户管理信息 - 主账户ID不为空时直接返回")
    void getAccountManagementInfoDTO_WithNonEmptyAccountId_ShouldReturnDirectly() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // 设置主账产品组和账户管理信息
            authRecordedDTO.setAccountGroupAuthControlList(java.util.Arrays.asList(primaryAccountGroupAuthControlDTO));
            AccountManagementInfoDTO existingAccountInfo = new AccountManagementInfoDTO();
            existingAccountInfo.setAccountManagementId("ACCT001");
            existingAccountInfo.setCustomerId("*********");
            authorizationCheckProcessingPayload.setAccountManagementInfoDTO(existingAccountInfo);

            // Act
            AccountManagementInfoDTO result = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);

            // Assert
            assertNotNull(result);
            assertEquals("ACCT001", result.getAccountManagementId());
            assertEquals("*********", result.getCustomerId());

            // 验证没有调用其他服务
            verify(commonAccountService, never()).selectByCusIdProNumAndCurr(any());
        }
    }

    @Test
    @DisplayName("获取账户管理信息 - 通过客户号和产品编号查询失败时构建默认账户")
    void getAccountManagementInfoDTO_WithFailedQuery_ShouldBuildDefaultAccount() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            authRecordedDTO.setAccountGroupAuthControlList(java.util.Arrays.asList(primaryAccountGroupAuthControlDTO));

            // 设置空的账户管理ID
            AccountManagementInfoDTO emptyAccountInfo = new AccountManagementInfoDTO();
            emptyAccountInfo.setAccountManagementId("");
            authorizationCheckProcessingPayload.setAccountManagementInfoDTO(emptyAccountInfo);

            // Mock commonAccountService返回null
            when(commonAccountService.selectByCusIdProNumAndCurr(any())).thenReturn(null);

            // Act
            AccountManagementInfoDTO result = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);

            // Assert
            assertNotNull(result);
            assertEquals(LocalDate.of(2023, 1, 1), result.getOpenDate());
            assertEquals(LocalDate.of(2023, 1, 1), result.getLastStatementDate());

            // 验证调用了commonAccountService
            verify(commonAccountService).selectByCusIdProNumAndCurr(any());
        }
    }

    @Test
    @DisplayName("汇率转换 - 正常转换成功场景")
    void converterRate_WithValidParameters_ShouldReturnConvertedAmountSuccessfully() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            String organizationNumber = "001";
            String sourceCurrencyCode = "840";
            String targetCurrencyCode = "156";
            BigDecimal amount = new BigDecimal("100.00");
            BigDecimal expectedResult = new BigDecimal("650.00");

            when(currencyCommonService.getCurrencyRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount))
                .thenReturn(expectedResult);

            // Act
            BigDecimal result = authCheckItemManager.converterRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount);

            // Assert
            assertNotNull(result);
            assertEquals(expectedResult, result);

            // 验证调用了汇率服务
            verify(currencyCommonService).getCurrencyRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount);
        }
    }

    @Test
    @DisplayName("汇率转换 - 金额为null时正常处理")
    void converterRate_WithNullAmount_ShouldCallService() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            String organizationNumber = "001";
            String sourceCurrencyCode = "840";
            String targetCurrencyCode = "156";
            BigDecimal amount = null;
            BigDecimal expectedResult = BigDecimal.ZERO;

            when(currencyCommonService.getCurrencyRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount))
                .thenReturn(expectedResult);

            // Act
            BigDecimal result = authCheckItemManager.converterRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount);

            // Assert
            assertNotNull(result);
            assertEquals(expectedResult, result);

            // 验证调用了汇率服务
            verify(currencyCommonService).getCurrencyRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount);
        }
    }

    @Test
    @DisplayName("汇率转换 - 汇率服务抛出异常时传播异常")
    void converterRate_WithServiceException_ShouldPropagateException() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            String organizationNumber = "001";
            String sourceCurrencyCode = "840";
            String targetCurrencyCode = "156";
            BigDecimal amount = new BigDecimal("100.00");

            when(currencyCommonService.getCurrencyRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount))
                .thenThrow(new RuntimeException("汇率服务异常"));

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                authCheckItemManager.converterRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount);
            });

            assertEquals("汇率服务异常", exception.getMessage());

            // 验证调用了汇率服务
            verify(currencyCommonService).getCurrencyRate(organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount);
        }
    }

    @Test
    @DisplayName("获取账户管理信息 - 测试buildOpenDate方法的逻辑")
    void getAccountManagementInfoDTO_TestBuildOpenDateLogic() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();

            // 设置不同的开户日期
            LocalDate customOpenDate = LocalDate.of(2022, 6, 15);
            cardAuthorizationDTO.setOpenDate(customOpenDate);

            // 设置主账产品组为null，这样会触发buildOpenDate逻辑
            authRecordedDTO.setAccountGroupAuthControlList(null);

            // Act
            AccountManagementInfoDTO result = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);

            // Assert
            assertNotNull(result);
            assertEquals(customOpenDate, result.getOpenDate());
            assertEquals(customOpenDate, result.getLastStatementDate());
        }
    }

    @Test
    @DisplayName("获取账户管理信息 - 测试CommonAccountDTO构建逻辑")
    void getAccountManagementInfoDTO_TestCommonAccountDTOBuilding() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            initializeTestData();
            authRecordedDTO.setAccountGroupAuthControlList(java.util.Arrays.asList(primaryAccountGroupAuthControlDTO));

            // 设置特定的测试数据
            authRecordedDTO.setAuthBillingCurrencyCode("840");
            authRecordedDTO.setAuthCustomerId("CUST123");
            authRecordedDTO.setOrganizationNumber("001");
            primaryAccountGroupAuthControlDTO.setAcctProductCode("PROD456");

            // 设置空的账户管理ID
            AccountManagementInfoDTO emptyAccountInfo = new AccountManagementInfoDTO();
            emptyAccountInfo.setAccountManagementId("");
            authorizationCheckProcessingPayload.setAccountManagementInfoDTO(emptyAccountInfo);

            // Mock返回的账户管理信息
            AccountManagementInfo mockAccountInfo = new AccountManagementInfo();
            mockAccountInfo.setCustomerId("CUST123");
            mockAccountInfo.setProductNumber("PROD456");
            mockAccountInfo.setOpenDate(LocalDate.of(2023, 3, 15));
            when(commonAccountService.selectByCusIdProNumAndCurr(any())).thenReturn(mockAccountInfo);

            // Act
            AccountManagementInfoDTO result = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);

            // Assert
            assertNotNull(result);
            assertEquals("CUST123", result.getCustomerId());
            assertEquals("PROD456", result.getProductNumber());
            assertEquals(LocalDate.of(2023, 3, 15), result.getOpenDate());

            // 验证调用了commonAccountService
            verify(commonAccountService).selectByCusIdProNumAndCurr(any(CommonAccountDTO.class));
        }
    }
}
