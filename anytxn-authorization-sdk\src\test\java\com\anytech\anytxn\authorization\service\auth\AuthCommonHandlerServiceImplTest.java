/**
 * @description 授权公共处理方法服务测试类
 * <AUTHOR>
 * @date 2025/07/07
 * @version 1.0
 * @AI 已复核
 */
package com.anytech.anytxn.authorization.service.auth;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import org.mockito.MockedStatic;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

import com.anytech.anytxn.authorization.client.encryption.AuthEncryptionFeignClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.anytech.anytxn.authorization.base.domain.dto.AuthCardReqDTO;
import com.anytech.anytxn.authorization.base.domain.dto.AuthCardRespDTO;
import com.anytech.anytxn.authorization.base.domain.dto.AuthCheckCodeDTO;
import com.anytech.anytxn.authorization.base.domain.dto.DCIDcs8583ReqDTO;
import com.anytech.anytxn.authorization.base.enums.IdentifyWayEnum;
import com.anytech.anytxn.authorization.base.enums.TranTypeDetailEnum;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthTransTypeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTransTypeTopCodeEnum;
import com.anytech.anytxn.business.base.encryption.dto.UnionResponseDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardBasicInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.card.model.CardBasicInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.limit.base.enums.TransDirection;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;

import java.util.Arrays;

@ExtendWith(MockitoExtension.class)
@DisplayName("AuthCommonHandlerServiceImpl单元测试")
class AuthCommonHandlerServiceImplTest {

    @Mock
    private IOrganizationInfoService organizationInfoService;

    @Mock
    private AuthEncryptionFeignClient authEncryptionFeignClient;

    @Mock
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Mock
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;

    @Mock
    private CardBasicInfoMapper cardBasicInfoMapper;

    @InjectMocks
    private AuthCommonHandlerServiceImpl authCommonHandlerService;

    private AuthRecordedDTO authRecordedDTO;
    private AuthCheckCodeDTO authCheckCodeDTO;
    private AuthCardReqDTO authCardReqDTO;
    private DCIDcs8583ReqDTO dcidcs8583ReqDTO;

    @BeforeEach
    void setUp() {
        // 使用MockedStatic创建需要OrgNumberUtils的对象以避免初始化问题
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // 创建基础测试数据
            authRecordedDTO = new AuthRecordedDTO();
            authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.AUTHORIZATION_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeDetailCode(TranTypeDetailEnum.VISA_CONSUMPTION.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
            authRecordedDTO.setAuthTransactionAmount(new BigDecimal("100.00"));
            authRecordedDTO.setAuthCardholderBillingAmount(new BigDecimal("100.00"));

            authCheckCodeDTO = new AuthCheckCodeDTO();
            authCheckCodeDTO.setAuthCheckType(IdentifyWayEnum.ASSIST_CVV.getCode());

            authCardReqDTO = new AuthCardReqDTO();
        }

        dcidcs8583ReqDTO = new DCIDcs8583ReqDTO();
    }

    @Test
    @DisplayName("测试Visa处理-授权消费交易")
    void testVisaHandler_AuthorizationConsumption() {
        // 准备数据
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.AUTHORIZATION_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeDetailCode(TranTypeDetailEnum.VISA_CONSUMPTION.getCode());

        // 执行方法
        String result = authCommonHandlerService.visaHandler(authRecordedDTO);

        // 验证结果
        assertThat(result).isEqualTo(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
    }

    @Test
    @DisplayName("测试Visa处理-授权取现交易")
    void testVisaHandler_AuthorizationCash() {
        // 准备数据
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.AUTHORIZATION_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeDetailCode(TranTypeDetailEnum.VISA_CASH.getCode());

        // 执行方法
        String result = authCommonHandlerService.visaHandler(authRecordedDTO);

        // 验证结果
        assertThat(result).isEqualTo(AuthTransTypeTopCodeEnum.CASH_TRANS.getCode());
    }

    @Test
    @DisplayName("测试方向处理-还款交易贷记")
    void testDirectionHandler_RepayCredit() {
        // 准备数据
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.REPAY_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());

        // 执行方法
        String result = authCommonHandlerService.directionHandler(authRecordedDTO);

        // 验证结果
        assertThat(result).isEqualTo(TransDirection.CREDIT.getCode());
    }

    @Test
    @DisplayName("测试方向处理-正常交易借记")
    void testDirectionHandler_NormalDebit() {
        // 准备数据
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());

        // 执行方法
        String result = authCommonHandlerService.directionHandler(authRecordedDTO);

        // 验证结果
        assertThat(result).isEqualTo(TransDirection.DEBIT.getCode());
    }

    @Test
    @DisplayName("测试持卡人账单金额处理-正常情况")
    void testAuthCardholderBillingAmount_Normal() {
        // 准备数据
        BigDecimal inputAmount = new BigDecimal("100.00");
        authRecordedDTO.setAuthCardholderBillingAmount(new BigDecimal("100.00"));
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        // 执行方法
        BigDecimal result = authCommonHandlerService.authCardholderBillingAmount(authRecordedDTO, inputAmount);

        // 验证结果
        assertThat(result).isEqualTo(new BigDecimal("100.00"));
    }

    @Test
    @DisplayName("测试持卡人账单金额处理-还款交易负数")
    void testAuthCardholderBillingAmount_RepayNegative() {
        // 准备数据
        BigDecimal inputAmount = new BigDecimal("100.00");
        authRecordedDTO.setAuthCardholderBillingAmount(new BigDecimal("100.00"));
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.REPAY_TRANS.getCode());

        // 执行方法
        BigDecimal result = authCommonHandlerService.authCardholderBillingAmount(authRecordedDTO, inputAmount);

        // 验证结果
        assertThat(result).isEqualTo(new BigDecimal("-100.00"));
    }

    @Test
    @DisplayName("测试金额处理-正常情况")
    void testAmountHandler_Normal() {
        // 准备数据
        BigDecimal inputAmount = new BigDecimal("100.00");
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("100.00"));

        // 执行方法
        BigDecimal result = authCommonHandlerService.amountHandler(authRecordedDTO, inputAmount);

        // 验证结果
        assertThat(result).isEqualTo(new BigDecimal("100.00"));
    }

    @Test
    @DisplayName("测试认证辅助类型-参数为空")
    void testAuthAssistType_NullParam() {
        // 执行方法
        String result = authCommonHandlerService.authAssistType(null);

        // 验证结果
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试边界条件-空参数处理")
    void testBoundaryConditions_NullParameters() {
        // 使用MockedStatic创建需要OrgNumberUtils的对象
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // 测试visaHandler空参数
            AuthRecordedDTO nullAuthRecorded = new AuthRecordedDTO();
            String visaResult = authCommonHandlerService.visaHandler(nullAuthRecorded);
            assertThat(visaResult).isNull();

            // 测试directionHandler空参数
            String directionResult = authCommonHandlerService.directionHandler(nullAuthRecorded);
            assertThat(directionResult).isNull();

            // 测试amountHandler空参数
            BigDecimal amountResult = authCommonHandlerService.amountHandler(nullAuthRecorded, BigDecimal.ZERO);
            assertThat(amountResult).isNull();
        }
    }

    @Test
    @DisplayName("测试认证辅助类型-CVV验证")
    void testAuthAssistType_CVV() {
        // 准备数据
        authCheckCodeDTO.setAuthCheckType(IdentifyWayEnum.ASSIST_CVV.getCode());
        authCheckCodeDTO.setCardNumber("1234567890123456");
        authCheckCodeDTO.setAvailableDate("2512");
        authCheckCodeDTO.setServiceCode("101");

        // Mock加密服务响应
        UnionResponseDTO mockResponse = new UnionResponseDTO();
        mockResponse.setCheckValue("123".getBytes());
        AnyTxnHttpResponse<UnionResponseDTO> mockHttpResponse = AnyTxnHttpResponse.success(mockResponse);
        when(authEncryptionFeignClient.createCvvByCw(any())).thenReturn(mockHttpResponse);

        // 执行方法
        String result = authCommonHandlerService.authAssistType(authCheckCodeDTO);

        // 验证结果
        assertThat(result).isEqualTo("123");
        verify(authEncryptionFeignClient).createCvvByCw(any());
    }

    @Test
    @DisplayName("测试认证辅助类型-CVV2验证")
    void testAuthAssistType_CVV2() {
        // 准备数据
        authCheckCodeDTO.setAuthCheckType(IdentifyWayEnum.ASSIST_CVV2.getCode());
        authCheckCodeDTO.setCardNumber("1234567890123456");
        authCheckCodeDTO.setAvailableDate("2512");
        authCheckCodeDTO.setServiceCode("101");

        // Mock加密服务响应
        UnionResponseDTO mockResponse = new UnionResponseDTO();
        mockResponse.setCheckValue("456".getBytes());
        AnyTxnHttpResponse<UnionResponseDTO> mockHttpResponse = AnyTxnHttpResponse.success(mockResponse);
        when(authEncryptionFeignClient.createCvvByCw(any())).thenReturn(mockHttpResponse);

        // 执行方法
        String result = authCommonHandlerService.authAssistType(authCheckCodeDTO);

        // 验证结果
        assertThat(result).isEqualTo("456");
        verify(authEncryptionFeignClient).createCvvByCw(any());
    }

    @Test
    @DisplayName("测试认证辅助类型-PIN验证")
    void testAuthAssistType_PIN() {
        // 准备数据
        authCheckCodeDTO.setAuthCheckType(IdentifyWayEnum.ASSIST_PIN.getCode());
        authCheckCodeDTO.setPlainText("1234");

        // 执行方法
        String result = authCommonHandlerService.authAssistType(authCheckCodeDTO);

        // 验证结果 - MD5前8位
        assertThat(result).hasSize(8);
        assertThat(result).isEqualTo("81dc9bdb");
    }

    @Test
    @DisplayName("测试获取卡片安全信息-参数为空")
    void testGetCardSafetyInfo_NullParam() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // 执行方法
            AuthCardRespDTO result = authCommonHandlerService.getCardSafetyInfo(null);

            // 验证结果
            assertThat(result).isNull();
        }
    }

    @Test
    @DisplayName("测试获取卡片安全信息-参数不完整")
    void testGetCardSafetyInfo_IncompleteParam() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // 准备不完整的数据
            authCardReqDTO.setApplicationNumber("APP123");
            // 缺少其他必要字段

            // 执行方法
            AuthCardRespDTO result = authCommonHandlerService.getCardSafetyInfo(authCardReqDTO);

            // 验证结果 - 实际返回的是空字段的对象，不是null
            assertThat(result).isNotNull();
            assertThat(result.getCardNumber()).isNull();
            assertThat(result.getExpiryDate()).isNull();
            assertThat(result.getCVV2()).isNull();
        }
    }

    @Test
    @DisplayName("测试DCS检验ARQC-参数为空")
    void testDcsCheckArqc_NullParam() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");

            // 执行方法
            String result = authCommonHandlerService.dcsCheckArqc(null, true);

            // 验证结果
            assertThat(result).isNull();
        }
    }

    @Test
    @DisplayName("测试amountHandler-更多分支覆盖")
    void testAmountHandler_MoreBranches() {
        // 准备数据 - 测试不同的交易类型
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.CASH_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("200.00"));

        // 执行方法
        BigDecimal result = authCommonHandlerService.amountHandler(authRecordedDTO, new BigDecimal("200.00"));

        // 验证结果
        assertThat(result).isEqualTo(new BigDecimal("200.00"));
    }

    @Test
    @DisplayName("测试authCardholderBillingAmount-更多分支覆盖")
    void testAuthCardholderBillingAmount_MoreBranches() {
        // 准备数据 - 测试有替换金额的情况
        authRecordedDTO.setAuthCardholderBillingAmount(new BigDecimal("300.00"));
        authRecordedDTO.setAuthReplaceAmountActualVisa(new BigDecimal("50.00"));
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        // 执行方法
        BigDecimal result = authCommonHandlerService.authCardholderBillingAmount(authRecordedDTO, new BigDecimal("300.00"));

        // 验证结果
        assertThat(result).isEqualTo(new BigDecimal("250.00"));
    }

    @Test
    @DisplayName("测试authCardholderBillingAmount-JCB替换金额")
    void testAuthCardholderBillingAmount_JcbReplacement() {
        // 准备数据 - 测试JCB替换金额的情况
        authRecordedDTO.setAuthCardholderBillingAmount(new BigDecimal("400.00"));
        authRecordedDTO.setAuthReplaceAmountActualJcb(new BigDecimal("100.00"));
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        // 执行方法
        BigDecimal result = authCommonHandlerService.authCardholderBillingAmount(authRecordedDTO, new BigDecimal("400.00"));

        // 验证结果
        assertThat(result).isEqualTo(new BigDecimal("300.00"));
    }

    @Test
    @DisplayName("测试visaHandler方法-VISA消费交易")
    void testVisaHandler_VisaConsumption() {
        // 准备数据
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.AUTHORIZATION_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeDetailCode(TranTypeDetailEnum.VISA_CONSUMPTION.getCode());

        // 执行方法
        String result = authCommonHandlerService.visaHandler(authRecordedDTO);

        // 验证结果
        assertThat(result).isEqualTo(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
    }

    @Test
    @DisplayName("测试visaHandler方法-VISA取现交易")
    void testVisaHandler_VisaCash() {
        // 准备数据
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.AUTHORIZATION_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeDetailCode(TranTypeDetailEnum.VISA_CASH.getCode());

        // 执行方法
        String result = authCommonHandlerService.visaHandler(authRecordedDTO);

        // 验证结果
        assertThat(result).isEqualTo(AuthTransTypeTopCodeEnum.CASH_TRANS.getCode());
    }

    @Test
    @DisplayName("测试visaHandler方法-非授权交易")
    void testVisaHandler_NonAuthorizationTrans() {
        // 准备数据
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeDetailCode(TranTypeDetailEnum.VISA_CONSUMPTION.getCode());

        // 执行方法
        String result = authCommonHandlerService.visaHandler(authRecordedDTO);

        // 验证结果 - 应该返回原始的交易大类
        assertThat(result).isEqualTo(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
    }

    @Test
    @DisplayName("测试directionHandler方法-还款交易贷记")
    void testDirectionHandler_RepayTransCredit() {
        // 准备数据
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.REPAY_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());

        // 执行方法
        String result = authCommonHandlerService.directionHandler(authRecordedDTO);

        // 验证结果
        assertThat(result).isEqualTo(TransDirection.CREDIT.getCode());
    }

    @Test
    @DisplayName("测试directionHandler方法-非还款交易")
    void testDirectionHandler_NonRepayTrans() {
        // 准备数据
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());

        // 执行方法
        String result = authCommonHandlerService.directionHandler(authRecordedDTO);

        // 验证结果 - 应该返回借记
        assertThat(result).isEqualTo(TransDirection.DEBIT.getCode());
    }

    @Test
    @DisplayName("测试authCardholderBillingAmount方法-还款交易取负值")
    void testAuthCardholderBillingAmount_RepayTransNegate() {
        // 准备数据
        authRecordedDTO.setAuthCardholderBillingAmount(new BigDecimal("500.00"));
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.REPAY_TRANS.getCode());

        // 执行方法
        BigDecimal result = authCommonHandlerService.authCardholderBillingAmount(authRecordedDTO, new BigDecimal("500.00"));

        // 验证结果 - 还款交易应该取负值
        assertThat(result).isEqualTo(new BigDecimal("-500.00"));
    }

    @Test
    @DisplayName("测试authCardholderBillingAmount方法-同时有VISA和JCB替换金额")
    void testAuthCardholderBillingAmount_BothVisaAndJcbReplacement() {
        // 准备数据
        authRecordedDTO.setAuthCardholderBillingAmount(new BigDecimal("600.00"));
        authRecordedDTO.setAuthReplaceAmountActualVisa(new BigDecimal("100.00"));
        authRecordedDTO.setAuthReplaceAmountActualJcb(new BigDecimal("50.00"));
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        // 执行方法
        BigDecimal result = authCommonHandlerService.authCardholderBillingAmount(authRecordedDTO, new BigDecimal("600.00"));

        // 验证结果 - 实际业务逻辑是减去JCB替换金额
        assertThat(result).isEqualTo(new BigDecimal("550.00"));
    }

    @Test
    @DisplayName("测试authAssistType方法-CVV验证完整流程")
    void testAuthAssistType_CVVCompleteFlow() {
        // 准备数据
        authCheckCodeDTO.setAuthCheckType(IdentifyWayEnum.ASSIST_CVV.getCode());
        authCheckCodeDTO.setCardNumber("1234567890123456");
        authCheckCodeDTO.setAvailableDate("2025");
        authCheckCodeDTO.setServiceCode("101");

        // Mock加密服务
        UnionResponseDTO unionResponseDTO = new UnionResponseDTO();
        unionResponseDTO.setCheckValue("123".getBytes());
        @SuppressWarnings("unchecked")
        AnyTxnHttpResponse<UnionResponseDTO> response = mock(AnyTxnHttpResponse.class);
        when(response.getData()).thenReturn(unionResponseDTO);
        when(authEncryptionFeignClient.createCvvByCw(any())).thenReturn(response);

        // 执行方法
        String result = authCommonHandlerService.authAssistType(authCheckCodeDTO);

        // 验证结果
        assertThat(result).isEqualTo("123");
        verify(authEncryptionFeignClient).createCvvByCw(any());
    }

    @Test
    @DisplayName("测试authAssistType方法-CVV2验证完整流程")
    void testAuthAssistType_CVV2CompleteFlow() {
        // 准备数据
        authCheckCodeDTO.setAuthCheckType(IdentifyWayEnum.ASSIST_CVV2.getCode());
        authCheckCodeDTO.setCardNumber("1234567890123456");
        authCheckCodeDTO.setAvailableDate("2025");
        authCheckCodeDTO.setServiceCode("101");

        // Mock加密服务
        UnionResponseDTO unionResponseDTO = new UnionResponseDTO();
        unionResponseDTO.setCheckValue("456".getBytes());
        @SuppressWarnings("unchecked")
        AnyTxnHttpResponse<UnionResponseDTO> response = mock(AnyTxnHttpResponse.class);
        when(response.getData()).thenReturn(unionResponseDTO);
        when(authEncryptionFeignClient.createCvvByCw(any())).thenReturn(response);

        // 执行方法
        String result = authCommonHandlerService.authAssistType(authCheckCodeDTO);

        // 验证结果
        assertThat(result).isEqualTo("456");
        verify(authEncryptionFeignClient).createCvvByCw(any());
    }

    @Test
    @DisplayName("测试authAssistType方法-加密服务异常")
    void testAuthAssistType_EncryptionServiceException() {
        // 准备数据
        authCheckCodeDTO.setAuthCheckType(IdentifyWayEnum.ASSIST_CVV.getCode());
        authCheckCodeDTO.setCardNumber("1234567890123456");
        authCheckCodeDTO.setAvailableDate("2025");
        authCheckCodeDTO.setServiceCode("101");

        // Mock加密服务抛出异常
        when(authEncryptionFeignClient.createCvvByCw(any())).thenThrow(new RuntimeException("加密服务异常"));

        // 执行方法并验证异常
        assertThatThrownBy(() -> authCommonHandlerService.authAssistType(authCheckCodeDTO))
            .isInstanceOf(AnyTxnAuthException.class);
    }

    @Test
    @DisplayName("测试authAssistType方法-加密服务返回空数据")
    void testAuthAssistType_EncryptionServiceNullData() {
        // 准备数据
        authCheckCodeDTO.setAuthCheckType(IdentifyWayEnum.ASSIST_CVV.getCode());
        authCheckCodeDTO.setCardNumber("1234567890123456");
        authCheckCodeDTO.setAvailableDate("2025");
        authCheckCodeDTO.setServiceCode("101");

        // Mock加密服务返回空数据
        @SuppressWarnings("unchecked")
        AnyTxnHttpResponse<UnionResponseDTO> response = mock(AnyTxnHttpResponse.class);
        when(response.getData()).thenReturn(null);
        when(authEncryptionFeignClient.createCvvByCw(any())).thenReturn(response);

        // 执行方法并验证异常
        assertThatThrownBy(() -> authCommonHandlerService.authAssistType(authCheckCodeDTO))
            .isInstanceOf(AnyTxnAuthException.class);
    }

    @Test
    @DisplayName("测试authAssistType方法-参数不完整CVV验证")
    void testAuthAssistType_IncompleteCVVParams() {
        // 准备不完整的数据
        authCheckCodeDTO.setAuthCheckType(IdentifyWayEnum.ASSIST_CVV.getCode());
        authCheckCodeDTO.setCardNumber("1234567890123456");
        // 缺少availableDate和serviceCode

        // 执行方法
        String result = authCommonHandlerService.authAssistType(authCheckCodeDTO);

        // 验证结果 - 参数不完整应该返回空字符串
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试authAssistType方法-参数不完整PIN验证")
    void testAuthAssistType_IncompletePINParams() {
        // 准备不完整的数据
        authCheckCodeDTO.setAuthCheckType(IdentifyWayEnum.ASSIST_PIN.getCode());
        // 缺少plainText

        // 执行方法
        String result = authCommonHandlerService.authAssistType(authCheckCodeDTO);

        // 验证结果 - 参数不完整应该返回空字符串
        assertThat(result).isEmpty();
    }
}