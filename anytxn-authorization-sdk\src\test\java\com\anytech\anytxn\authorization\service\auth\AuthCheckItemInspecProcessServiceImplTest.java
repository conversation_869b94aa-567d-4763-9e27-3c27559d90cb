/**
 * @description AuthCheckItemInspecProcessServiceImpl的单元测试类
 * <AUTHOR>
 * @date 2025/07/08
 * @version 1.0
 * @AI 已复核
 */
package com.anytech.anytxn.authorization.service.auth;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.DataResponse;
import com.anytech.anytxn.authorization.base.domain.dto.ParmAuthCheckControlDTO;
import com.anytech.anytxn.authorization.base.enums.CheckMethodEnum;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.service.auth.checkitem.CardOverUseItem;
import com.anytech.anytxn.authorization.service.auth.checkitem.CheckBeforeRuleItem;
import com.anytech.anytxn.authorization.service.auth.checkitem.CheckNotCardPresentSwitchItem;
import com.anytech.anytxn.authorization.service.auth.checkitem.CheckReqInfoConflictItem;
import com.anytech.anytxn.authorization.service.channel.upi.UpiAuthCheckWayDetailServiceImpl;
import com.anytech.anytxn.authorization.service.rule.RuleTransferImpl;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthCheckItemEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthRepDetailEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckControlResDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckDefinitionResDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthCheckControlService;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthCheckDefinitionService;

@ExtendWith(MockitoExtension.class)
@DisplayName("AuthCheckItemInspecProcessServiceImpl单元测试")
class AuthCheckItemInspecProcessServiceImplTest {

    @Mock
    private RuleTransferImpl ruleTransferService;

    @Mock
    private AuthCheckWayDetailServiceImpl authCheckWayDetailService;

    @Mock
    private IAuthCheckDefinitionService authCheckDefinitionService;

    @Mock
    private IAuthCheckControlService authCheckControlService;

    @Mock
    private Map<String, AbstractCheckItem> checkItemMap;

    @Mock
    private CardOverUseItem cardOverUseItem;

    @Mock
    private CheckReqInfoConflictItem checkReqInfoConflictItem;

    @Mock
    private CheckNotCardPresentSwitchItem checkNotCardPresentSwitchItem;

    @Mock
    private CheckBeforeRuleItem checkBeforeRuleItem;

    @Mock
    private UpiAuthCheckWayDetailServiceImpl upiAuthCheckWayDetailService;

    @InjectMocks
    private AuthCheckItemInspecProcessServiceImpl authCheckItemInspecProcessService;

    private AuthorizationCheckProcessingPayload processingPayload;
    private AuthRecordedDTO authRecordedDTO;
    private CardAuthorizationDTO cardAuthorizationDTO;
    private Future<DataResponse<Map<String, Object>>> dataResponseFuture;
    private List<ParmAuthCheckControlDTO> authCheckControlDTOList;
    private AuthCheckDefinitionResDTO authCheckDefinitionResDTO;
    private List<AuthCheckControlResDTO> authCheckControlResList;

    @BeforeEach
    void setUp() {
        // 创建Future（这个不需要OrgNumberUtils）
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("result", "success");
        DataResponse<Map<String, Object>> dataResponse = new DataResponse<>();
        dataResponse.setData(responseData);
        dataResponseFuture = CompletableFuture.completedFuture(dataResponse);
    }

    /**
     * 创建测试数据的帮助方法
     */
    private void createTestData() {
        // 创建基础测试数据
        authRecordedDTO = new AuthRecordedDTO();
        authRecordedDTO.setAuthCardNumber("1234567890123456");
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("100.00"));

        cardAuthorizationDTO = new CardAuthorizationDTO();
        cardAuthorizationDTO.setCardNumber("1234567890123456");
        cardAuthorizationDTO.setOrganizationNumber("ORG001");
        cardAuthorizationDTO.setProductNumber("PROD001");

        processingPayload = new AuthorizationCheckProcessingPayload();
        processingPayload.setAuthRecordedDTO(authRecordedDTO);
        processingPayload.setCardAuthorizationDTO(cardAuthorizationDTO);

        // 创建检查控制列表
        authCheckControlDTOList = new ArrayList<>();
        ParmAuthCheckControlDTO checkControlDTO = new ParmAuthCheckControlDTO();
        checkControlDTO.setCheckItem(AuthCheckItemEnum.CARD_STATUS.getCheckItem());
        checkControlDTO.setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
        checkControlDTO.setCheckResponseCode(AuthResponseCodeEnum.SUCCESS_CHECK_CODE.getCode());
        checkControlDTO.setCheckPriority(1);
        authCheckControlDTOList.add(checkControlDTO);

        // 创建检查定义
        authCheckDefinitionResDTO = new AuthCheckDefinitionResDTO();
        authCheckDefinitionResDTO.setTableId("TABLE001");
        authCheckDefinitionResDTO.setCheckMethod(CheckMethodEnum.INSPECT_ALL_PRIORITY.getCode());

        // 创建检查控制
        AuthCheckControlResDTO authCheckControlResDTO = new AuthCheckControlResDTO();
        authCheckControlResDTO.setTableId("TABLE001");
        authCheckControlResDTO.setCheckItem(AuthCheckItemEnum.CARD_STATUS.getCheckItem());
        authCheckControlResDTO.setCheckPriority(1);
        
        authCheckControlResList = new ArrayList<>();
        authCheckControlResList.add(authCheckControlResDTO);
    }

    @Test
    @DisplayName("测试processAuthCheck方法-正常通过")
    void testProcessAuthCheck_Success() throws IOException {
        // 使用MockedStatic包装对象创建，避免OrgNumberUtils为null的问题
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            // 创建测试数据
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(authCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.processAuthCheck(processingPayload, null);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckControlService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckWayDetailService).authCheckConfirmOne(any(), any());
        }
    }

    @Test
    @DisplayName("测试processAuthCheck方法-检查失败")
    void testProcessAuthCheck_CheckFailed() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(authCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.processAuthCheck(processingPayload, null);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
        }
    }

    @Test
    @DisplayName("测试processAuthCheck方法-异常情况")
    void testProcessAuthCheck_Exception() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            when(ruleTransferService.getAuthCheckRule(any())).thenThrow(new RuntimeException("规则引擎异常"));

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckItemInspecProcessService.processAuthCheck(processingPayload, null))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("规则引擎异常");
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
        }
    }

    @Test
    @DisplayName("测试processAuthCheck方法-带Future参数")
    void testProcessAuthCheck_WithFuture() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(authCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.processAuthCheck(processingPayload, dataResponseFuture);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckControlService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckWayDetailService).authCheckConfirmOne(any(), any());
        }
    }

    @Test
    @DisplayName("测试upiProcessAuthCheck方法-正常通过")
    void testUpiProcessAuthCheck_Success() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(upiAuthCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.upiProcessAuthCheck(processingPayload, null);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckControlService).findByOrgAndTableId(anyString(), anyString());
            verify(upiAuthCheckWayDetailService).authCheckConfirmOne(any(), any());
        }
    }

    @Test
    @DisplayName("测试upiProcessAuthCheck方法-检查失败")
    void testUpiProcessAuthCheck_CheckFailed() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(upiAuthCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.upiProcessAuthCheck(processingPayload, null);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
        }
    }

    @Test
    @DisplayName("测试dciProcessAuthCheck方法-正常通过")
    void testDciProcessAuthCheck_Success() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(authCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.dciProcessAuthCheck(processingPayload);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckControlService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckWayDetailService).authCheckConfirmOne(any(), any());
        }
    }

    @Test
    @DisplayName("测试dciProcessAuthCheck方法-检查失败")
    void testDciProcessAuthCheck_CheckFailed() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(authCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.dciProcessAuthCheck(processingPayload);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
        }
    }

    @Test
    @DisplayName("测试tableIdGet方法-正常情况")
    void testTableIdGet_Success() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);

            // 执行方法
            String result = authCheckItemInspecProcessService.tableIdGet(processingPayload);

            // 验证结果
            assertThat(result).isEqualTo("TABLE001");
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
        }
    }

    @Test
    @DisplayName("测试tableIdGet方法-返回空值")
    void testTableIdGet_EmptyResult() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckItemInspecProcessService.tableIdGet(processingPayload))
                .isInstanceOf(AnyTxnAuthException.class);
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
        }
    }

    @Test
    @DisplayName("测试tableIdGet方法-规则对象为空")
    void testTableIdGet_NullRuleObject() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(null);

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckItemInspecProcessService.tableIdGet(processingPayload))
                .isInstanceOf(AnyTxnAuthException.class);
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
        }
    }

    @Test
    @DisplayName("测试边界条件-空参数处理")
    void testBoundaryConditions_NullParameters() {
        // 测试空的processingPayload
        assertThatThrownBy(() -> authCheckItemInspecProcessService.tableIdGet(null))
            .isInstanceOf(Exception.class);
    }

    @Test
    @DisplayName("测试边界条件-空的检查控制列表")
    void testBoundaryConditions_EmptyCheckControlList() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(new ArrayList<>());

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckItemInspecProcessService.processAuthCheck(processingPayload, null))
                .isInstanceOf(AnyTxnAuthException.class);
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckControlService).findByOrgAndTableId(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试边界条件-检查方法为空")
    void testBoundaryConditions_EmptyCheckMethod() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            authCheckDefinitionResDTO.setCheckMethod("");
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckItemInspecProcessService.processAuthCheck(processingPayload, null))
                .isInstanceOf(AnyTxnAuthException.class);
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试异常情况-检查定义为空")
    void testExceptionHandling_NullCheckDefinition() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(null);

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckItemInspecProcessService.processAuthCheck(processingPayload, null))
                .isInstanceOf(AnyTxnAuthException.class);
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试异常情况-检查控制服务调用失败")
    void testExceptionHandling_CheckControlServiceFailure() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenThrow(new RuntimeException("检查控制服务异常"));

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckItemInspecProcessService.processAuthCheck(processingPayload, null))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("检查控制服务异常");
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckControlService).findByOrgAndTableId(anyString(), anyString());
        }
    }

    @Test
    @DisplayName("测试复杂场景-多个检查项混合")
    void testComplexScenario_MultipleCheckItems() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据 - 多个检查项
            List<AuthCheckControlResDTO> multipleCheckControls = new ArrayList<>();
            
            AuthCheckControlResDTO checkControl1 = new AuthCheckControlResDTO();
            checkControl1.setTableId("TABLE001");
            checkControl1.setCheckItem(AuthCheckItemEnum.CARD_STATUS.getCheckItem());
            checkControl1.setCheckPriority(1);
            multipleCheckControls.add(checkControl1);
            
            AuthCheckControlResDTO checkControl2 = new AuthCheckControlResDTO();
            checkControl2.setTableId("TABLE001");
            checkControl2.setCheckItem(AuthCheckItemEnum.CARD_BLOCK_CODE.getCheckItem());
            checkControl2.setCheckPriority(2);
            multipleCheckControls.add(checkControl2);
            
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(multipleCheckControls);
            when(authCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.processAuthCheck(processingPayload, null);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckControlService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckWayDetailService).authCheckConfirmOne(any(), any());
        }
    }

    @Test
    @DisplayName("测试不同检查方式-检查所有项按第一个拒绝项响应")
    void testDifferentCheckMethod_InspectAllFirst() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            authCheckDefinitionResDTO.setCheckMethod(CheckMethodEnum.INSPECT_ALL_FIRST.getCode());
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(authCheckWayDetailService.authCheckConfirmTwo(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.processAuthCheck(processingPayload, null);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            
            // 验证方法调用
            verify(authCheckWayDetailService).authCheckConfirmTwo(any(), any());
        }
    }

    @Test
    @DisplayName("测试异常返回码处理")
    void testExceptionCodeHandling() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();
            
            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");
            
            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(authCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.processAuthCheck(processingPayload, null);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());
            
            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
        }
    }

    @Test
    @DisplayName("测试upiProcessAuthCheck方法-正常通过场景")
    void testUpiProcessAuthCheck_NormalSuccess() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");

            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(upiAuthCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.upiProcessAuthCheck(processingPayload, null);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckControlService).findByOrgAndTableId(anyString(), anyString());
            verify(upiAuthCheckWayDetailService).authCheckConfirmOne(any(), any());
        }
    }

    @Test
    @DisplayName("测试upiProcessAuthCheck方法-检查第一个拒绝项就响应")
    void testUpiProcessAuthCheck_InspectFirst() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 准备数据
            authCheckDefinitionResDTO.setCheckMethod(CheckMethodEnum.INSPECT_FIRST.getCode());
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");

            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(upiAuthCheckWayDetailService.authCheckConfirmThree(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.upiProcessAuthCheck(processingPayload, null);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // 验证方法调用
            verify(upiAuthCheckWayDetailService).authCheckConfirmThree(any(), any());
        }
    }

    @Test
    @DisplayName("测试upiProcessAuthCheck方法-异常情况")
    void testUpiProcessAuthCheck_Exception() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 准备数据
            when(ruleTransferService.getAuthCheckRule(any())).thenThrow(new RuntimeException("UPI规则引擎异常"));

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckItemInspecProcessService.upiProcessAuthCheck(processingPayload, null))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("UPI规则引擎异常");

            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
        }
    }

    @Test
    @DisplayName("测试processAuthCheck方法-检查方式为检查第一个拒绝项就响应")
    void testProcessAuthCheck_InspectFirstReject() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 准备数据
            authCheckDefinitionResDTO.setCheckMethod(CheckMethodEnum.INSPECT_FIRST.getCode());
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");

            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(authCheckWayDetailService.authCheckConfirmThree(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // 执行方法
            int result = authCheckItemInspecProcessService.processAuthCheck(processingPayload, null);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());

            // 验证方法调用
            verify(authCheckWayDetailService).authCheckConfirmThree(any(), any());
        }
    }

    @Test
    @DisplayName("测试processAuthCheck方法-无效的检查方式抛出异常")
    void testProcessAuthCheck_InvalidCheckMethod_ThrowsException() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 准备数据
            authCheckDefinitionResDTO.setCheckMethod("INVALID_METHOD");
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");

            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckItemInspecProcessService.processAuthCheck(processingPayload, null))
                .isInstanceOf(AnyTxnAuthException.class);
        }
    }

    @Test
    @DisplayName("测试dciProcessAuthCheck方法-异常情况")
    void testDciProcessAuthCheck_Exception() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 准备数据
            when(ruleTransferService.getAuthCheckRule(any())).thenThrow(new RuntimeException("DCI规则引擎异常"));

            // 执行方法并验证异常
            assertThatThrownBy(() -> authCheckItemInspecProcessService.dciProcessAuthCheck(processingPayload))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("DCI规则引擎异常");

            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
        }
    }

    @Test
    @DisplayName("测试processAuthCheck方法-带有dataResponseFuture参数")
    void testProcessAuthCheck_WithDataResponseFuture() throws IOException {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 准备数据
            Map<String, String> authCheckObject = new HashMap<>();
            authCheckObject.put(AuthConstans.TABLE_ID, "TABLE001");

            when(ruleTransferService.getAuthCheckRule(any())).thenReturn(authCheckObject);
            when(authCheckDefinitionService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckDefinitionResDTO);
            when(authCheckControlService.findByOrgAndTableId(anyString(), anyString()))
                .thenReturn(authCheckControlResList);
            when(authCheckWayDetailService.authCheckConfirmOne(any(), any()))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 创建一个Mock的Future对象
            @SuppressWarnings("unchecked")
            Future<DataResponse<Map<String, Object>>> mockFuture = mock(Future.class);

            // 执行方法
            int result = authCheckItemInspecProcessService.processAuthCheck(processingPayload, mockFuture);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // 验证方法调用
            verify(ruleTransferService).getAuthCheckRule(any());
            verify(authCheckDefinitionService).findByOrgAndTableId(anyString(), anyString());
            verify(authCheckControlService).findByOrgAndTableId(anyString(), anyString());
        }
    }
}