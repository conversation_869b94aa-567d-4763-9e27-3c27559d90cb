/**
 * @description AuthCheckWayDetailServiceImpl的单元测试类
 * <AUTHOR>
 * @date 2025/07/08
 * @version 1.0
 * @AI 已复核
 */
package com.anytech.anytxn.authorization.service.auth;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.ParmAuthCheckControlDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTrancactionStatusEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTransactionSourceCodeEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

@ExtendWith(MockitoExtension.class)
@DisplayName("AuthCheckWayDetailServiceImpl单元测试")
class AuthCheckWayDetailServiceImplTest {

    @InjectMocks
    private AuthCheckWayDetailServiceImpl authCheckWayDetailService;

    private AuthRecordedDTO authRecordedDTO;
    private List<ParmAuthCheckControlDTO> checkControlDtoList;

    @BeforeEach
    void setUp() {
        // 在每个测试方法中创建测试数据，避免OrgNumberUtils问题
    }

    /**
     * 创建测试数据的帮助方法
     */
    private void createTestData() {
        authRecordedDTO = new AuthRecordedDTO();
        authRecordedDTO.setAuthCustomerId("CUSTOMER001");
        authRecordedDTO.setAuthTransactionSourceCode(AuthTransactionSourceCodeEnum.ONUS.getCode());
        authRecordedDTO.setPreAuthComplete(false);
        authRecordedDTO.setAuthAuthIdentificationResponse("123456");

        checkControlDtoList = new ArrayList<>();
    }

    /**
     * 创建ParmAuthCheckControlDTO的帮助方法
     */
    private ParmAuthCheckControlDTO createCheckControlDTO(String checkResult, String responseCode, 
                                                          Integer checkPriority, Integer responsePriority) {
        ParmAuthCheckControlDTO dto = new ParmAuthCheckControlDTO();
        dto.setCheckResult(checkResult);
        dto.setCheckResponseCode(responseCode);
        dto.setCheckPriority(checkPriority);
        dto.setResponsePriority(responsePriority);
        return dto;
    }

    // ========== authCheckConfirmOne 方法测试 ==========

    @Test
    @DisplayName("测试authCheckConfirmOne方法-全部通过场景")
    void testAuthCheckConfirmOne_AllApproved() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建全部通过的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 2, 2));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmOne(checkControlDtoList, authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
            assertThat(authRecordedDTO.getAuthAuthCode()).isNotNull().hasSize(6);
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmOne方法-有拒绝项按响应优先级")
    void testAuthCheckConfirmOne_WithRejectByResponsePriority() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建有拒绝项的检查控制列表，按响应优先级排序
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "51", 1, 2)); // 低响应优先级
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "05", 2, 1)); // 高响应优先级
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 3, 3));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmOne(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 应该选择响应优先级最高的拒绝项
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo("05"); // 响应优先级为1的响应码
            assertThat(authRecordedDTO.getAuthTrancactionStatus()).isEqualTo(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmOne方法-空列表")
    void testAuthCheckConfirmOne_EmptyList() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            checkControlDtoList = new ArrayList<>(); // 空列表

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmOne(checkControlDtoList, authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmOne方法-null列表")
    void testAuthCheckConfirmOne_NullList() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 执行方法 - 期望抛出NullPointerException
            assertThatThrownBy(() -> authCheckWayDetailService.authCheckConfirmOne(null, authRecordedDTO))
                .isInstanceOf(NullPointerException.class);
        }
    }

    // ========== authCheckConfirmTwo 方法测试 ==========

    @Test
    @DisplayName("测试authCheckConfirmTwo方法-全部通过场景")
    void testAuthCheckConfirmTwo_AllApproved() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建全部通过的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 2, 2));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmTwo(checkControlDtoList, authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
            assertThat(authRecordedDTO.getAuthAuthCode()).isNotNull().hasSize(6);
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmTwo方法-有拒绝项按检查优先级")
    void testAuthCheckConfirmTwo_WithRejectByCheckPriority() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建有拒绝项的检查控制列表，按检查优先级排序
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "51", 2, 1)); // 低检查优先级
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "05", 1, 2)); // 高检查优先级
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 3, 3));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmTwo(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 应该选择检查优先级最高的拒绝项
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo("05"); // 检查优先级为1的响应码
            assertThat(authRecordedDTO.getAuthTrancactionStatus()).isEqualTo(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmTwo方法-预授权完成场景")
    void testAuthCheckConfirmTwo_PreAuthComplete() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 设置预授权完成
            authRecordedDTO.setPreAuthComplete(true);
            authRecordedDTO.setAuthAuthIdentificationResponse("654321");

            // 创建全部通过的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmTwo(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 预授权完成时使用预设的授权码
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthAuthCode()).isEqualTo("654321");
        }
    }

    // ========== authCheckConfirmThree 方法测试 ==========

    @Test
    @DisplayName("测试authCheckConfirmThree方法-找到第一个拒绝项")
    void testAuthCheckConfirmThree_FirstRejectFound() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建有拒绝项的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "51", 2, 2)); // 第一个拒绝项
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "05", 3, 3)); // 不会执行到

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 遇到第一个拒绝项就返回
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo("51");
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-没有拒绝项ONUS交易")
    void testAuthCheckConfirmThree_NoRejectOnus() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 设置为ONUS交易
            authRecordedDTO.setAuthTransactionSourceCode(AuthTransactionSourceCodeEnum.ONUS.getCode());

            // 创建全部通过的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 2, 2));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - ONUS交易使用APPROVED_BY_ISSUER响应码
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
            assertThat(authRecordedDTO.getAuthTrancactionStatus()).isEqualTo(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
            assertThat(authRecordedDTO.getAuthAuthCode()).isNotNull().hasSize(6);
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-没有拒绝项MASTERCARD交易")
    void testAuthCheckConfirmThree_NoRejectMastercard() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 设置为MASTERCARD交易
            authRecordedDTO.setAuthTransactionSourceCode(AuthTransactionSourceCodeEnum.MASTERCARD.getCode());

            // 创建全部通过的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - MASTERCARD交易使用CHECK_RESPONSE响应码
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
            assertThat(authRecordedDTO.getAuthTrancactionStatus()).isEqualTo(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-没有拒绝项VISA交易")
    void testAuthCheckConfirmThree_NoRejectVisa() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 设置为VISA交易
            authRecordedDTO.setAuthTransactionSourceCode(AuthTransactionSourceCodeEnum.VISA.getCode());

            // 创建全部通过的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - VISA交易使用CHECK_RESPONSE响应码
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-没有拒绝项UPI交易")
    void testAuthCheckConfirmThree_NoRejectUpi() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 设置为UPI交易
            authRecordedDTO.setAuthTransactionSourceCode(AuthTransactionSourceCodeEnum.UPI.getCode());

            // 创建全部通过的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - UPI交易使用CHECK_RESPONSE响应码
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-MASTERCARD交易有拒绝项")
    void testAuthCheckConfirmThree_MastercardWithReject() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 设置为MASTERCARD交易
            authRecordedDTO.setAuthTransactionSourceCode(AuthTransactionSourceCodeEnum.MASTERCARD.getCode());

            // 创建有拒绝项的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "51", 1, 1));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - MASTERCARD交易有拒绝项时不设置响应码
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isNull(); // MASTERCARD不设置响应码
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-空列表")
    void testAuthCheckConfirmThree_EmptyList() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            checkControlDtoList = new ArrayList<>(); // 空列表

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 空列表时直接通过
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-预授权完成场景")
    void testAuthCheckConfirmThree_PreAuthComplete() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 设置预授权完成
            authRecordedDTO.setPreAuthComplete(true);
            authRecordedDTO.setAuthAuthIdentificationResponse("999888");

            // 创建全部通过的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 预授权完成时使用预设的授权码
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthAuthCode()).isEqualTo("999888");
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-已有响应码场景")
    void testAuthCheckConfirmThree_ExistingResponseCode() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 设置已有响应码
            authRecordedDTO.setAuthResponseCode("88");

            // 创建全部通过的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 已有响应码时保持不变
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo("88");
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-按检查优先级排序")
    void testAuthCheckConfirmThree_SortByCheckPriority() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建乱序的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 3, 3));
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "51", 1, 1)); // 优先级最高的拒绝项
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 2, 2));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 应该找到优先级最高的拒绝项
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo("51");
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmOne方法-部分检查结果为null")
    void testAuthCheckConfirmOne_PartialNullCheckResults() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建包含null检查结果的列表
            ParmAuthCheckControlDTO nullResultDto = createCheckControlDTO(null, "00", 1, 1);
            nullResultDto.setCheckResult(null);
            checkControlDtoList.add(nullResultDto);
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 2, 2));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmOne(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 只有非null的检查结果被考虑
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmTwo方法-混合检查结果包含null")
    void testAuthCheckConfirmTwo_MixedResultsWithNull() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建混合检查结果的列表
            ParmAuthCheckControlDTO nullResultDto = createCheckControlDTO(null, "00", 1, 1);
            nullResultDto.setCheckResult(null);
            checkControlDtoList.add(nullResultDto);
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "05", 2, 1));
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 3, 2));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmTwo(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 应该选择拒绝项
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo("05");
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-空检查控制列表")
    void testAuthCheckConfirmThree_EmptyControlList() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            checkControlDtoList = new ArrayList<>(); // 空列表

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 空列表应该返回通过
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthTrancactionStatus()).isEqualTo(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmOne方法-所有检查结果都为null")
    void testAuthCheckConfirmOne_AllNullCheckResults() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建所有检查结果都为null的列表
            ParmAuthCheckControlDTO nullResultDto1 = createCheckControlDTO(null, "00", 1, 1);
            nullResultDto1.setCheckResult(null);
            ParmAuthCheckControlDTO nullResultDto2 = createCheckControlDTO(null, "00", 2, 2);
            nullResultDto2.setCheckResult(null);
            checkControlDtoList.add(nullResultDto1);
            checkControlDtoList.add(nullResultDto2);

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmOne(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 所有检查结果为null时应该返回通过
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmTwo方法-单个拒绝项")
    void testAuthCheckConfirmTwo_SingleRejectItem() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建只有一个拒绝项的列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "14", 1, 1));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmTwo(checkControlDtoList, authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo("14");
            assertThat(authRecordedDTO.getAuthTrancactionStatus()).isEqualTo(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmThree方法-多个拒绝项返回第一个")
    void testAuthCheckConfirmThree_MultipleRejectItemsReturnFirst() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 创建多个拒绝项的列表，按检查优先级排序
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "05", 2, 2)); // 第二个拒绝项
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "51", 1, 1)); // 第一个拒绝项（优先级最高）
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 3, 3));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmThree(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 应该返回第一个拒绝项
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.REJECT_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo("51"); // 优先级最高的拒绝项
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmOne方法-预授权完成且有拒绝项")
    void testAuthCheckConfirmOne_PreAuthCompleteWithReject() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 设置预授权完成
            authRecordedDTO.setPreAuthComplete(true);
            authRecordedDTO.setAuthAuthIdentificationResponse("123456");

            // 创建有拒绝项的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_REJECT, "05", 1, 1));
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 2, 2));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmOne(checkControlDtoList, authRecordedDTO);

            // 验证结果
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo("05");
            assertThat(authRecordedDTO.getAuthTrancactionStatus()).isEqualTo(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
        }
    }

    @Test
    @DisplayName("测试authCheckConfirmTwo方法-预授权完成且全部通过")
    void testAuthCheckConfirmTwo_PreAuthCompleteAllApproved() {
        try (MockedStatic<OrgNumberUtils> mockedStatic = mockStatic(OrgNumberUtils.class)) {
            mockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestData();

            // 设置预授权完成
            authRecordedDTO.setPreAuthComplete(true);
            authRecordedDTO.setAuthAuthIdentificationResponse("654321");

            // 创建全部通过的检查控制列表
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 1, 1));
            checkControlDtoList.add(createCheckControlDTO(AuthConstans.AUTH_CHECK_RESULT_APPROVE, "00", 2, 2));

            // 执行方法
            int result = authCheckWayDetailService.authCheckConfirmTwo(checkControlDtoList, authRecordedDTO);

            // 验证结果 - 预授权完成时使用预设的授权码
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthResponseCode()).isEqualTo(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
            assertThat(authRecordedDTO.getAuthAuthCode()).isEqualTo("654321");
        }
    }
}