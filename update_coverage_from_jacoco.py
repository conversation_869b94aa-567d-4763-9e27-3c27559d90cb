#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从 jacoco CSV 报告中提取覆盖率数据并更新 MD 文件
"""

import csv
import re
import os

def parse_jacoco_csv(csv_file_path):
    """解析 jacoco CSV 文件，返回类名到覆盖率的映射"""
    coverage_data = {}
    
    with open(csv_file_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            class_name = row['CLASS']
            if not class_name:
                continue
                
            # 计算行覆盖率
            line_missed = int(row['LINE_MISSED'])
            line_covered = int(row['LINE_COVERED'])
            total_lines = line_missed + line_covered
            
            if total_lines > 0:
                coverage_percent = (line_covered / total_lines) * 100
                coverage_data[class_name] = coverage_percent
            else:
                coverage_data[class_name] = 0.0
                
    return coverage_data

def update_md_file(md_file_path, coverage_data):
    """更新 MD 文件中的覆盖率数据"""
    
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    updated_lines = []
    
    for line in lines:
        # 查找表格行，格式类似：| 业务类名称 | 单元测试类名称 | 业务类行数 | 状态 |
        if '|' in line and '✅已完成' in line:
            parts = [part.strip() for part in line.split('|')]
            if len(parts) >= 6:  # 至少包含业务类名称、测试类名称、行数、状态等列
                business_class = parts[1].strip()
                test_class = parts[2].strip()
                
                # 查找对应的覆盖率数据
                coverage_percent = None
                for class_name, coverage in coverage_data.items():
                    if business_class in class_name:
                        coverage_percent = coverage
                        break
                
                if coverage_percent is not None:
                    # 更新覆盖率数据
                    if len(parts) >= 7:
                        # 如果已有覆盖率列，更新它
                        parts[5] = f" {coverage_percent:.1f}% "
                        if coverage_percent >= 80:
                            parts[6] = " ✅ 达标 "
                        else:
                            parts[6] = " ❌ 未达标 "
                    else:
                        # 如果没有覆盖率列，添加它
                        parts.append(f" {coverage_percent:.1f}% ")
                        if coverage_percent >= 80:
                            parts.append(" ✅ 达标 ")
                        else:
                            parts.append(" ❌ 未达标 ")
                    
                    line = '|'.join(parts)
        
        updated_lines.append(line)
    
    # 写回文件
    with open(md_file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(updated_lines))

def main():
    # 文件路径
    jacoco_csv_path = 'anytxn-authorization-sdk/target/site/jacoco/jacoco.csv'
    md_file_path = 'doc/单元测试合并版.md'
    
    if not os.path.exists(jacoco_csv_path):
        print(f"错误：找不到 jacoco CSV 文件：{jacoco_csv_path}")
        return
    
    if not os.path.exists(md_file_path):
        print(f"错误：找不到 MD 文件：{md_file_path}")
        return
    
    print("正在解析 jacoco 覆盖率数据...")
    coverage_data = parse_jacoco_csv(jacoco_csv_path)
    print(f"解析到 {len(coverage_data)} 个类的覆盖率数据")
    
    print("正在更新 MD 文件...")
    update_md_file(md_file_path, coverage_data)
    print("MD 文件更新完成！")
    
    # 统计达标情况
    达标_count = 0
    未达标_count = 0
    
    for class_name, coverage in coverage_data.items():
        if coverage >= 80:
            达标_count += 1
        else:
            未达标_count += 1
    
    print(f"\n覆盖率统计：")
    print(f"达标（≥80%）: {达标_count} 个类")
    print(f"未达标（<80%）: {未达标_count} 个类")
    print(f"总体达标率: {达标_count/(达标_count+未达标_count)*100:.1f}%")

if __name__ == '__main__':
    main()
