package com.anytech.anytxn.authorization.service.rule;

import com.anytech.anytxn.limit.base.domain.dto.payload.TransBaseInfoDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCtrlUnitService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

/**
 * @description AuthMatchRuleServiceImpl单元测试 - 简化版本
 * <AUTHOR>
 * @date 2024-12-19
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("授权规则匹配服务测试")
class AuthMatchRuleServiceImplTest {

    @InjectMocks
    private AuthMatchRuleServiceImpl authMatchRuleService;

    @Mock
    private ITransactionCtrlUnitService transactionCtrlUnitService;

    // 测试数据
    private OrganizationInfoResDTO orgInfo;
    private TransBaseInfoDTO transBaseInfoDTO;

    @BeforeEach
    void setUp() {
        // 使用mock对象避免OrgNumberUtils依赖问题
        orgInfo = mock(OrganizationInfoResDTO.class);
        when(orgInfo.getOrganizationNumber()).thenReturn("ORG001");

        transBaseInfoDTO = new TransBaseInfoDTO();
        transBaseInfoDTO.setMerchantId("MERCHANT001");
        transBaseInfoDTO.setCountryCode("156");
        transBaseInfoDTO.setMccCode("5411");
        transBaseInfoDTO.setAuthTxnCode("00");
        transBaseInfoDTO.setPostTxnCode("01");
        transBaseInfoDTO.setCardProductCode("PROD001");
        transBaseInfoDTO.setOrganizationNumber("ORG001");
        transBaseInfoDTO.setAccountCurrency("CNY");
        transBaseInfoDTO.setGroupType("INDIVIDUAL");
        transBaseInfoDTO.setHierarchyLevel("1");
    }

    @Test
    @DisplayName("测试matchRule方法 - 空参数")
    void testMatchRule_NullTransBaseInfo() {
        // 执行测试
        String result = authMatchRuleService.matchRule(null, orgInfo);

        // 验证结果
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getLimitCtrlUnit方法 - 空交易管控单元编码")
    void testGetLimitCtrlUnit_EmptyTransCtrlUnitCode() {
        // 执行测试
        var result = authMatchRuleService.getLimitCtrlUnit("ORG001", "");

        // 验证结果
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getLimitCtrlUnit方法 - null交易管控单元编码")
    void testGetLimitCtrlUnit_NullTransCtrlUnitCode() {
        // 执行测试
        var result = authMatchRuleService.getLimitCtrlUnit("ORG001", null);

        // 验证结果
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getLimitCtrlUnit方法 - 管控单元未找到")
    void testGetLimitCtrlUnit_CtrlUnitNotFound() {
        // Arrange
        when(transactionCtrlUnitService.findByUnitCodeAndOrg("ORG001", "CTRL001"))
            .thenReturn(null);

        // 执行测试
        var result = authMatchRuleService.getLimitCtrlUnit("ORG001", "CTRL001");

        // 验证结果
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getLimitCtrlUnit方法 - 成功获取")
    void testGetLimitCtrlUnit_Success() {
        // Arrange
        com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDTO ctrlUnitDTO =
            new com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDTO();
        ctrlUnitDTO.setTransactionCtrlUnitDebits(Arrays.asList(
            createTransactionCtrlUnitDebitDTO()
        ));

        when(transactionCtrlUnitService.findByUnitCodeAndOrg("ORG001", "CTRL001"))
            .thenReturn(ctrlUnitDTO);

        // 执行测试
        var result = authMatchRuleService.getLimitCtrlUnit("ORG001", "CTRL001");

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
    }

    private com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDebitDTO createTransactionCtrlUnitDebitDTO() {
        com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDebitDTO debitDTO =
            new com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCtrlUnitDebitDTO();
        debitDTO.setLimitCtrlUnitId("LIMIT001");
        debitDTO.setNumber(1);
        debitDTO.setTransactionTypeCode("01");
        return debitDTO;
    }
}
