package com.anytech.anytxn.authorization.service.channel.onus;

import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.base.enums.OnusAuthTransTypeEnum;
import com.anytech.anytxn.authorization.base.service.manager.ApplicationManager;
import com.anytech.anytxn.authorization.service.channel.AbstractTransRoutingService;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * OnusTransactionClassifyServiceImpl单元测试
 * <AUTHOR>
 * @date 2025/01/07
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ONUS交易分类服务测试")
class OnusTransactionClassifyServiceImplTest {

    @InjectMocks
    private OnusTransactionClassifyServiceImpl onusTransactionClassifyService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private AbstractTransRoutingService transRoutingService;

    private AuthRecordedDTO authRecordedDTO;
    private ISO8583DTO iso8583DTO;

    @BeforeEach
    void setUp() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");
            authRecordedDTO = new AuthRecordedDTO();
        }
        iso8583DTO = new ISO8583DTO();

        // 设置ApplicationManager的静态字段
        ApplicationManager.applicationContext = applicationContext;
    }

    @Test
    @DisplayName("处理授权交易_服务存在时成功调用")
    void testProcessAuthTrans_ServiceExists() throws Exception {
        try (MockedStatic<OnusAuthTransTypeEnum> onusAuthTransTypeEnumMock = mockStatic(OnusAuthTransTypeEnum.class)) {
            // Given
            String transType = "onus_purchase";
            String serviceName = transType + "_Service";
            
            onusAuthTransTypeEnumMock.when(() -> OnusAuthTransTypeEnum.getTransType(iso8583DTO))
                    .thenReturn(transType);
            
            when(applicationContext.containsBean(serviceName)).thenReturn(true);
            when(applicationContext.getBean(serviceName, AbstractTransRoutingService.class))
                    .thenReturn(transRoutingService);

            // When
            int result = onusTransactionClassifyService.processAuthTrans(authRecordedDTO, iso8583DTO);

            // Then
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthTransType()).isEqualTo(transType);
            
            verify(applicationContext).containsBean(serviceName);
            verify(applicationContext).getBean(serviceName, AbstractTransRoutingService.class);
            verify(transRoutingService).trans(authRecordedDTO, iso8583DTO);
        }
    }

    @Test
    @DisplayName("处理授权交易_服务不存在时跳过调用")
    void testProcessAuthTrans_ServiceNotExists() throws Exception {
        try (MockedStatic<OnusAuthTransTypeEnum> onusAuthTransTypeEnumMock = mockStatic(OnusAuthTransTypeEnum.class)) {
            // Given
            String transType = "onus_unknown";
            String serviceName = transType + "_Service";
            
            onusAuthTransTypeEnumMock.when(() -> OnusAuthTransTypeEnum.getTransType(iso8583DTO))
                    .thenReturn(transType);
            
            when(applicationContext.containsBean(serviceName)).thenReturn(false);

            // When
            int result = onusTransactionClassifyService.processAuthTrans(authRecordedDTO, iso8583DTO);

            // Then
            assertThat(result).isEqualTo(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            assertThat(authRecordedDTO.getAuthTransType()).isEqualTo(transType);
            
            verify(applicationContext).containsBean(serviceName);
            verify(applicationContext, never()).getBean(anyString(), eq(AbstractTransRoutingService.class));
            verifyNoInteractions(transRoutingService);
        }
    }
}
