package com.anytech.anytxn.authorization.service.channel.dci;

import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.base.service.dci.IDciAuthTransPreprocessService;
import com.anytech.anytxn.authorization.base.service.dci.IDciResponse8583HandlerService;
import com.anytech.anytxn.authorization.base.service.dci.IDciTransactionClassifyService;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTrancactionStatusEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DciHandlerAuthServiceImpl单元测试
 * <AUTHOR>
 * @date 2025/07/09
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class DciHandlerAuthServiceImplTest {

    @Mock
    private IDciAuthTransPreprocessService authTransPreprocessService;

    @Mock
    private IDciTransactionClassifyService transactionClassifyService;

    @Mock
    private IDciResponse8583HandlerService response8583HandlerService;

    @InjectMocks
    private DciHandlerAuthServiceImpl dciHandlerAuthService;

    private ISO8583DTO inputIso8583DTO;
    private ISO8583DTO outputIso8583DTO;
    private AuthRecordedDTO authRecordedDTO;

    @BeforeEach
    void setUp() {
        // 使用MockedStatic来Mock OrgNumberUtils静态工具类
        try (MockedStatic<OrgNumberUtils> mockedOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockedOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // 创建输入ISO8583DTO测试数据
            inputIso8583DTO = new ISO8583DTO();
            inputIso8583DTO.setMTI("0100");
            Map<Integer, String> inputFieldMap = new HashMap<>();
            inputFieldMap.put(2, "****************");
            inputFieldMap.put(3, "000000");
            inputFieldMap.put(4, "000000001000");
            inputFieldMap.put(11, "123456");
            inputIso8583DTO.setFieldMap(inputFieldMap);

            // 创建输出ISO8583DTO测试数据
            outputIso8583DTO = new ISO8583DTO();
            outputIso8583DTO.setMTI("0110");
            Map<Integer, String> outputFieldMap = new HashMap<>();
            outputFieldMap.put(2, "****************");
            outputFieldMap.put(3, "000000");
            outputFieldMap.put(4, "000000001000");
            outputFieldMap.put(11, "123456");
            outputFieldMap.put(39, "00");
            outputIso8583DTO.setFieldMap(outputFieldMap);

            // 创建AuthRecordedDTO测试数据
            authRecordedDTO = new AuthRecordedDTO();
            authRecordedDTO.setAuthGlobalFlowNumber("FLOW_123456789");
            authRecordedDTO.setAuthMessageTypeId("0100");
            authRecordedDTO.setAuthCardNumber("****************");
            authRecordedDTO.setAuthProcessingCode("000000");
            authRecordedDTO.setAuthTransactionAmount(new BigDecimal("10.00"));
            authRecordedDTO.setAuthTransactionTypeTopCode("01");
            authRecordedDTO.setAuthTransactionTypeDetailCode("001");
            authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
            authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        }
    }

    @Test
    @DisplayName("测试processAuth - 正常授权流程")
    void testProcessAuth_NormalFlow() throws Exception {
        // Arrange
        when(authTransPreprocessService.preProcessAuthTrans(inputIso8583DTO)).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(authRecordedDTO, inputIso8583DTO);
        when(response8583HandlerService.buildResponse(inputIso8583DTO, authRecordedDTO)).thenReturn(outputIso8583DTO);

        // Act
        ISO8583DTO result = dciHandlerAuthService.processAuth(inputIso8583DTO);

        // Assert
        assertNotNull(result);
        assertEquals("0110", result.getMTI());
        assertEquals("****************", result.getFieldMap().get(2));
        assertEquals("00", result.getFieldMap().get(39));

        // 验证调用顺序和参数
        verify(authTransPreprocessService).preProcessAuthTrans(inputIso8583DTO);
        verify(transactionClassifyService).processAuthTrans(authRecordedDTO, inputIso8583DTO);
        verify(response8583HandlerService).buildResponse(inputIso8583DTO, authRecordedDTO);
    }

    @Test
    @DisplayName("测试processAuth - 交易预处理异常")
    void testProcessAuth_PreprocessException() throws Exception {
        // Arrange
        IOException expectedException = new IOException("交易预处理失败");
        when(authTransPreprocessService.preProcessAuthTrans(inputIso8583DTO)).thenThrow(expectedException);

        // Act & Assert
        IOException thrownException = assertThrows(IOException.class, () -> {
            dciHandlerAuthService.processAuth(inputIso8583DTO);
        });

        assertEquals("交易预处理失败", thrownException.getMessage());
        verify(authTransPreprocessService).preProcessAuthTrans(inputIso8583DTO);
        verify(transactionClassifyService, never()).processAuthTrans(any(), any());
        verify(response8583HandlerService, never()).buildResponse(any(), any());
    }

    @Test
    @DisplayName("测试processAuth - 交易分类处理异常")
    void testProcessAuth_TransactionClassifyException() throws Exception {
        // Arrange
        RuntimeException expectedException = new RuntimeException("交易分类处理失败");
        when(authTransPreprocessService.preProcessAuthTrans(inputIso8583DTO)).thenReturn(authRecordedDTO);
        doThrow(expectedException).when(transactionClassifyService).processAuthTrans(authRecordedDTO, inputIso8583DTO);

        // Act & Assert
        RuntimeException thrownException = assertThrows(RuntimeException.class, () -> {
            dciHandlerAuthService.processAuth(inputIso8583DTO);
        });

        assertEquals("交易分类处理失败", thrownException.getMessage());
        verify(authTransPreprocessService).preProcessAuthTrans(inputIso8583DTO);
        verify(transactionClassifyService).processAuthTrans(authRecordedDTO, inputIso8583DTO);
        verify(response8583HandlerService, never()).buildResponse(any(), any());
    }

    @Test
    @DisplayName("测试processAuth - 响应报文构建异常")
    void testProcessAuth_ResponseBuildException() throws Exception {
        // Arrange
        RuntimeException expectedException = new RuntimeException("响应报文构建失败");
        when(authTransPreprocessService.preProcessAuthTrans(inputIso8583DTO)).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(authRecordedDTO, inputIso8583DTO);
        when(response8583HandlerService.buildResponse(inputIso8583DTO, authRecordedDTO)).thenThrow(expectedException);

        // Act & Assert
        RuntimeException thrownException = assertThrows(RuntimeException.class, () -> {
            dciHandlerAuthService.processAuth(inputIso8583DTO);
        });

        assertEquals("响应报文构建失败", thrownException.getMessage());
        verify(authTransPreprocessService).preProcessAuthTrans(inputIso8583DTO);
        verify(transactionClassifyService).processAuthTrans(authRecordedDTO, inputIso8583DTO);
        verify(response8583HandlerService).buildResponse(inputIso8583DTO, authRecordedDTO);
    }

    @Test
    @DisplayName("测试processAuth - 空输入参数")
    void testProcessAuth_NullInput() throws Exception {
        // Arrange
        when(authTransPreprocessService.preProcessAuthTrans(null)).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(authRecordedDTO, null);
        when(response8583HandlerService.buildResponse(null, authRecordedDTO)).thenReturn(outputIso8583DTO);

        // Act
        ISO8583DTO result = dciHandlerAuthService.processAuth(null);

        // Assert
        assertNotNull(result);
        verify(authTransPreprocessService).preProcessAuthTrans(null);
        verify(transactionClassifyService).processAuthTrans(authRecordedDTO, null);
        verify(response8583HandlerService).buildResponse(null, authRecordedDTO);
    }

    @Test
    @DisplayName("测试processAuth - 验证方法调用顺序")
    void testProcessAuth_VerifyCallOrder() throws Exception {
        // Arrange
        when(authTransPreprocessService.preProcessAuthTrans(inputIso8583DTO)).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(authRecordedDTO, inputIso8583DTO);
        when(response8583HandlerService.buildResponse(inputIso8583DTO, authRecordedDTO)).thenReturn(outputIso8583DTO);

        // Act
        dciHandlerAuthService.processAuth(inputIso8583DTO);

        // Assert - 验证调用顺序
        var inOrder = inOrder(authTransPreprocessService, transactionClassifyService, response8583HandlerService);
        inOrder.verify(authTransPreprocessService).preProcessAuthTrans(inputIso8583DTO);
        inOrder.verify(transactionClassifyService).processAuthTrans(authRecordedDTO, inputIso8583DTO);
        inOrder.verify(response8583HandlerService).buildResponse(inputIso8583DTO, authRecordedDTO);
    }

    @Test
    @DisplayName("测试processAuth - 验证参数传递")
    void testProcessAuth_VerifyParameterPassing() throws Exception {
        // Arrange
        when(authTransPreprocessService.preProcessAuthTrans(inputIso8583DTO)).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(authRecordedDTO, inputIso8583DTO);
        when(response8583HandlerService.buildResponse(inputIso8583DTO, authRecordedDTO)).thenReturn(outputIso8583DTO);

        // Act
        dciHandlerAuthService.processAuth(inputIso8583DTO);

        // Assert - 验证传递的参数是正确的
        verify(authTransPreprocessService).preProcessAuthTrans(eq(inputIso8583DTO));
        verify(transactionClassifyService).processAuthTrans(eq(authRecordedDTO), eq(inputIso8583DTO));
        verify(response8583HandlerService).buildResponse(eq(inputIso8583DTO), eq(authRecordedDTO));
    }

    @Test
    @DisplayName("测试processAuth - 多次调用验证")
    void testProcessAuth_MultipleCallsVerification() throws Exception {
        // Arrange
        when(authTransPreprocessService.preProcessAuthTrans(any())).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(any(), any());
        when(response8583HandlerService.buildResponse(any(), any())).thenReturn(outputIso8583DTO);

        // Act
        dciHandlerAuthService.processAuth(inputIso8583DTO);
        dciHandlerAuthService.processAuth(inputIso8583DTO);

        // Assert - 验证每个方法都被调用了两次
        verify(authTransPreprocessService, times(2)).preProcessAuthTrans(any());
        verify(transactionClassifyService, times(2)).processAuthTrans(any(), any());
        verify(response8583HandlerService, times(2)).buildResponse(any(), any());
    }

    @Test
    @DisplayName("测试processAuth - 边界值测试")
    void testProcessAuth_BoundaryValues() throws Exception {
        // Arrange
        ISO8583DTO emptyIso8583DTO = new ISO8583DTO();
        AuthRecordedDTO emptyAuthRecordedDTO = new AuthRecordedDTO();
        ISO8583DTO emptyOutputIso8583DTO = new ISO8583DTO();

        when(authTransPreprocessService.preProcessAuthTrans(emptyIso8583DTO)).thenReturn(emptyAuthRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(emptyAuthRecordedDTO, emptyIso8583DTO);
        when(response8583HandlerService.buildResponse(emptyIso8583DTO, emptyAuthRecordedDTO)).thenReturn(emptyOutputIso8583DTO);

        // Act
        ISO8583DTO result = dciHandlerAuthService.processAuth(emptyIso8583DTO);

        // Assert
        assertNotNull(result);
        assertSame(emptyOutputIso8583DTO, result);
        verify(authTransPreprocessService).preProcessAuthTrans(emptyIso8583DTO);
        verify(transactionClassifyService).processAuthTrans(emptyAuthRecordedDTO, emptyIso8583DTO);
        verify(response8583HandlerService).buildResponse(emptyIso8583DTO, emptyAuthRecordedDTO);
    }

    @Test
    @DisplayName("测试processAuth - 方法签名验证")
    void testProcessAuth_MethodSignature() throws NoSuchMethodException {
        // 验证方法存在且签名正确
        java.lang.reflect.Method method = DciHandlerAuthServiceImpl.class
                .getDeclaredMethod("processAuth", ISO8583DTO.class);

        assertNotNull(method);
        assertEquals("processAuth", method.getName());
        assertEquals(ISO8583DTO.class, method.getReturnType());
        assertEquals(1, method.getParameterCount());
        assertEquals(ISO8583DTO.class, method.getParameterTypes()[0]);
    }

    @Test
    @DisplayName("测试processAuth - 类注解和依赖注入验证")
    void testProcessAuth_ClassAnnotationsAndDI() {
        // 验证依赖注入
        assertNotNull(dciHandlerAuthService);
        assertNotNull(authTransPreprocessService);
        assertNotNull(transactionClassifyService);
        assertNotNull(response8583HandlerService);

        // 验证类信息
        assertEquals("DciHandlerAuthServiceImpl", DciHandlerAuthServiceImpl.class.getSimpleName());
        assertFalse(DciHandlerAuthServiceImpl.class.isInterface());
    }

    @Test
    @DisplayName("测试processAuth - 返回值非空验证")
    void testProcessAuth_ReturnValueNotNull() throws Exception {
        // Arrange
        when(authTransPreprocessService.preProcessAuthTrans(any())).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(any(), any());
        when(response8583HandlerService.buildResponse(any(), any())).thenReturn(outputIso8583DTO);

        // Act
        ISO8583DTO result = dciHandlerAuthService.processAuth(inputIso8583DTO);

        // Assert
        assertNotNull(result);
        assertSame(outputIso8583DTO, result);
    }

    @Test
    @DisplayName("测试processAuth - 服务方法调用验证")
    void testProcessAuth_ServiceMethodCalls() throws Exception {
        // Arrange
        when(authTransPreprocessService.preProcessAuthTrans(inputIso8583DTO)).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(authRecordedDTO, inputIso8583DTO);
        when(response8583HandlerService.buildResponse(inputIso8583DTO, authRecordedDTO)).thenReturn(outputIso8583DTO);

        // Act
        dciHandlerAuthService.processAuth(inputIso8583DTO);

        // Assert - 验证每个服务方法都被调用了一次
        verify(authTransPreprocessService, times(1)).preProcessAuthTrans(inputIso8583DTO);
        verify(transactionClassifyService, times(1)).processAuthTrans(authRecordedDTO, inputIso8583DTO);
        verify(response8583HandlerService, times(1)).buildResponse(inputIso8583DTO, authRecordedDTO);

        // 验证没有其他方法被调用
        verifyNoMoreInteractions(authTransPreprocessService);
        verifyNoMoreInteractions(transactionClassifyService);
        verifyNoMoreInteractions(response8583HandlerService);
    }

    @Test
    @DisplayName("测试processAuth - 异常链传播验证")
    void testProcessAuth_ExceptionChainPropagation() throws Exception {
        // Arrange
        Exception originalException = new Exception("原始异常");
        IOException wrappedException = new IOException("包装异常", originalException);
        when(authTransPreprocessService.preProcessAuthTrans(inputIso8583DTO)).thenThrow(wrappedException);

        // Act & Assert
        IOException thrownException = assertThrows(IOException.class, () -> {
            dciHandlerAuthService.processAuth(inputIso8583DTO);
        });

        assertEquals("包装异常", thrownException.getMessage());
        assertEquals(originalException, thrownException.getCause());
    }

    @Test
    @DisplayName("测试processAuth - 性能测试")
    void testProcessAuth_Performance() throws Exception {
        // Arrange
        when(authTransPreprocessService.preProcessAuthTrans(any())).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(any(), any());
        when(response8583HandlerService.buildResponse(any(), any())).thenReturn(outputIso8583DTO);

        // Act - 测试多次调用的性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            dciHandlerAuthService.processAuth(inputIso8583DTO);
        }
        long endTime = System.currentTimeMillis();

        // Assert - 100次调用应该在合理时间内完成
        assertTrue(endTime - startTime < 1000, "100次方法调用应该在1秒内完成");
        verify(authTransPreprocessService, times(100)).preProcessAuthTrans(inputIso8583DTO);
    }

    @Test
    @DisplayName("测试processAuth - 内存使用验证")
    void testProcessAuth_MemoryUsage() throws Exception {
        // Arrange
        when(authTransPreprocessService.preProcessAuthTrans(any())).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(any(), any());
        when(response8583HandlerService.buildResponse(any(), any())).thenReturn(outputIso8583DTO);

        // Act - 多次调用不应该造成内存泄漏
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();

        for (int i = 0; i < 1000; i++) {
            dciHandlerAuthService.processAuth(inputIso8583DTO);
        }

        System.gc(); // 建议垃圾回收
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();

        // Assert - 内存使用不应该显著增加
        long memoryIncrease = finalMemory - initialMemory;
        assertTrue(memoryIncrease < 10 * 1024 * 1024, "方法调用不应该造成显著的内存增长"); // 小于10MB
    }

    @Test
    @DisplayName("测试processAuth - 线程安全性验证")
    void testProcessAuth_ThreadSafety() throws Exception {
        // Arrange
        when(authTransPreprocessService.preProcessAuthTrans(any())).thenReturn(authRecordedDTO);
        doNothing().when(transactionClassifyService).processAuthTrans(any(), any());
        when(response8583HandlerService.buildResponse(any(), any())).thenReturn(outputIso8583DTO);

        final boolean[] success = {true};

        Thread thread1 = new Thread(() -> {
            try {
                for (int i = 0; i < 50; i++) {
                    dciHandlerAuthService.processAuth(inputIso8583DTO);
                }
            } catch (Exception e) {
                success[0] = false;
            }
        });

        Thread thread2 = new Thread(() -> {
            try {
                for (int i = 0; i < 50; i++) {
                    dciHandlerAuthService.processAuth(inputIso8583DTO);
                }
            } catch (Exception e) {
                success[0] = false;
            }
        });

        // Act
        thread1.start();
        thread2.start();

        thread1.join();
        thread2.join();

        // Assert - 多线程调用应该都成功
        assertTrue(success[0], "多线程调用processAuth方法应该成功");
    }
}
