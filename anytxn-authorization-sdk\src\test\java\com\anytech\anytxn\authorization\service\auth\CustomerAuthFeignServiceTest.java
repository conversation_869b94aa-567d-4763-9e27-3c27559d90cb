/**
 * @description CustomerAuthFeignService的单元测试类
 * <AUTHOR>
 * @date 2025/07/07
 * @version 1.0
 * @AI 已复核
 */
package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.client.cardholder.AuthCardHolderFeign;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerBasicInfoDTO;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("CustomerAuthFeignService - 客户授权Feign服务测试")
class CustomerAuthFeignServiceTest {

    @Mock
    private AuthCardHolderFeign authCardHolderFeign;
    
    @InjectMocks
    private CustomerAuthFeignService customerAuthFeignService;
    
    @Mock
    private AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> customerAuthResponse;
    
    @Mock
    private AnyTxnHttpResponse<CustomerBasicInfoDTO> customerBasicResponse;
    
    @Mock
    private CustomerAuthorizationInfoDTO customerAuthorizationInfoDTO;
    
    @Mock
    private CustomerBasicInfoDTO customerBasicInfoDTO;
    
    private MockedStatic<CustAccountBO> custAccountBOMock;
    
    private final String TEST_ORG_NUMBER = "123456";
    private final String TEST_CUSTOMER_ID = "CUST001";
    private final String TEST_SUB_CUSTOMER = "SUB001";
    
    @BeforeEach
    void setUp() {
        custAccountBOMock = mockStatic(CustAccountBO.class);
        
        // 设置基本Mock行为
        lenient().when(customerAuthResponse.getData()).thenReturn(customerAuthorizationInfoDTO);
        lenient().when(customerBasicResponse.getData()).thenReturn(customerBasicInfoDTO);
    }
    
    @AfterEach
    void tearDown() {
        custAccountBOMock.close();
    }
    
    @Test
    @DisplayName("获取客户授权信息 - 非批处理模式直接调用Feign")
    void getCustomerAuthorizationByCustomerId_NonBatchMode_CallsFeignDirectly() {
        // Arrange
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID))
                .thenReturn(customerAuthResponse);
        
        // Act
        CustomerAuthorizationInfoDTO result = customerAuthFeignService.getCustomerAuthorizationByCustomerId(TEST_ORG_NUMBER, TEST_CUSTOMER_ID);
        
        // Assert
        assertThat(result).isEqualTo(customerAuthorizationInfoDTO);
        verify(authCardHolderFeign).getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID);
        verify(customerAuthResponse).getData();
    }
    
    @Test
    @DisplayName("获取客户授权信息 - null参数处理")
    void getCustomerAuthorizationByCustomerId_NullParameters_HandlesGracefully() {
        // Arrange
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(null, null, null))
                .thenReturn(customerAuthResponse);
        
        // Act
        CustomerAuthorizationInfoDTO result = customerAuthFeignService.getCustomerAuthorizationByCustomerId(null, null);
        
        // Assert
        assertThat(result).isEqualTo(customerAuthorizationInfoDTO);
        verify(authCardHolderFeign).getCustomerAuthInfoOrgAndCid(null, null, null);
    }
    
    @Test
    @DisplayName("获取客户授权信息 - 空字符串参数")
    void getCustomerAuthorizationByCustomerId_EmptyStringParameters_HandlesGracefully() {
        // Arrange
        String emptyString = "";
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(emptyString, emptyString, emptyString))
                .thenReturn(customerAuthResponse);
        
        // Act
        CustomerAuthorizationInfoDTO result = customerAuthFeignService.getCustomerAuthorizationByCustomerId(emptyString, emptyString);
        
        // Assert
        assertThat(result).isEqualTo(customerAuthorizationInfoDTO);
        verify(authCardHolderFeign).getCustomerAuthInfoOrgAndCid(emptyString, emptyString, emptyString);
    }
    
    @Test
    @DisplayName("获取客户授权信息 - Feign返回null响应抛出异常")
    void getCustomerAuthorizationByCustomerId_FeignReturnsNull_ThrowsNullPointerException() {
        // Arrange
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID))
                .thenReturn(null);
        
        // Act & Assert
        assertThatThrownBy(() -> customerAuthFeignService.getCustomerAuthorizationByCustomerId(TEST_ORG_NUMBER, TEST_CUSTOMER_ID))
                .isInstanceOf(NullPointerException.class);
        verify(authCardHolderFeign).getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID);
    }
    
    @Test
    @DisplayName("获取客户授权信息 - Feign响应数据为null")
    void getCustomerAuthorizationByCustomerId_FeignResponseDataIsNull_ReturnsNull() {
        // Arrange
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID))
                .thenReturn(customerAuthResponse);
        when(customerAuthResponse.getData()).thenReturn(null);
        
        // Act
        CustomerAuthorizationInfoDTO result = customerAuthFeignService.getCustomerAuthorizationByCustomerId(TEST_ORG_NUMBER, TEST_CUSTOMER_ID);
        
        // Assert
        assertThat(result).isNull();
        verify(authCardHolderFeign).getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID);
        verify(customerAuthResponse).getData();
    }
    
    @Test
    @DisplayName("根据机构号和客户ID获取客户基础信息 - 成功路径")
    void findByOrgAndSubCustomerId_ValidParameters_ReturnsCustomerBasicInfo() {
        // Arrange
        when(authCardHolderFeign.getCustomerBasicInfoByOrgAndCid(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER, TEST_SUB_CUSTOMER))
                .thenReturn(customerBasicResponse);
        
        // Act
        CustomerBasicInfoDTO result = customerAuthFeignService.findByOrgAndSubCustomerId(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER);
        
        // Assert
        assertThat(result).isEqualTo(customerBasicInfoDTO);
        verify(authCardHolderFeign).getCustomerBasicInfoByOrgAndCid(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER, TEST_SUB_CUSTOMER);
        verify(customerBasicResponse).getData();
    }
    
    @Test
    @DisplayName("根据机构号和客户ID获取客户基础信息 - null参数")
    void findByOrgAndSubCustomerId_NullParameters_HandlesGracefully() {
        // Arrange
        when(authCardHolderFeign.getCustomerBasicInfoByOrgAndCid(null, null, null))
                .thenReturn(customerBasicResponse);
        
        // Act
        CustomerBasicInfoDTO result = customerAuthFeignService.findByOrgAndSubCustomerId(null, null);
        
        // Assert
        assertThat(result).isEqualTo(customerBasicInfoDTO);
        verify(authCardHolderFeign).getCustomerBasicInfoByOrgAndCid(null, null, null);
    }
    
    @Test
    @DisplayName("根据机构号和客户ID获取客户基础信息 - 空字符串参数")
    void findByOrgAndSubCustomerId_EmptyStringParameters_HandlesGracefully() {
        // Arrange
        String emptyString = "";
        when(authCardHolderFeign.getCustomerBasicInfoByOrgAndCid(emptyString, emptyString, emptyString))
                .thenReturn(customerBasicResponse);
        
        // Act
        CustomerBasicInfoDTO result = customerAuthFeignService.findByOrgAndSubCustomerId(emptyString, emptyString);
        
        // Assert
        assertThat(result).isEqualTo(customerBasicInfoDTO);
        verify(authCardHolderFeign).getCustomerBasicInfoByOrgAndCid(emptyString, emptyString, emptyString);
    }
    
    @Test
    @DisplayName("根据机构号和客户ID获取客户基础信息 - Feign返回null抛出异常")
    void findByOrgAndSubCustomerId_FeignReturnsNull_ThrowsNullPointerException() {
        // Arrange
        when(authCardHolderFeign.getCustomerBasicInfoByOrgAndCid(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER, TEST_SUB_CUSTOMER))
                .thenReturn(null);
        
        // Act & Assert
        assertThatThrownBy(() -> customerAuthFeignService.findByOrgAndSubCustomerId(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER))
                .isInstanceOf(NullPointerException.class);
        verify(authCardHolderFeign).getCustomerBasicInfoByOrgAndCid(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER, TEST_SUB_CUSTOMER);
    }
    
    @Test
    @DisplayName("根据机构号和客户ID获取客户基础信息 - 响应数据为null")
    void findByOrgAndSubCustomerId_ResponseDataIsNull_ReturnsNull() {
        // Arrange
        when(authCardHolderFeign.getCustomerBasicInfoByOrgAndCid(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER, TEST_SUB_CUSTOMER))
                .thenReturn(customerBasicResponse);
        when(customerBasicResponse.getData()).thenReturn(null);
        
        // Act
        CustomerBasicInfoDTO result = customerAuthFeignService.findByOrgAndSubCustomerId(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER);
        
        // Assert
        assertThat(result).isNull();
        verify(authCardHolderFeign).getCustomerBasicInfoByOrgAndCid(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER, TEST_SUB_CUSTOMER);
        verify(customerBasicResponse).getData();
    }
    
    @Test
    @DisplayName("边界条件 - 大字符串参数处理")
    void getCustomerAuthorizationByCustomerId_LargeStringParameters_HandlesGracefully() {
        // Arrange
        String largeOrgNumber = "A".repeat(1000);
        String largeCustomerId = "B".repeat(1000);
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(largeOrgNumber, largeCustomerId, largeCustomerId))
                .thenReturn(customerAuthResponse);
        
        // Act
        CustomerAuthorizationInfoDTO result = customerAuthFeignService.getCustomerAuthorizationByCustomerId(largeOrgNumber, largeCustomerId);
        
        // Assert
        assertThat(result).isEqualTo(customerAuthorizationInfoDTO);
        verify(authCardHolderFeign).getCustomerAuthInfoOrgAndCid(largeOrgNumber, largeCustomerId, largeCustomerId);
    }
    
    @Test
    @DisplayName("边界条件 - 特殊字符参数处理")
    void findByOrgAndSubCustomerId_SpecialCharacterParameters_HandlesGracefully() {
        // Arrange
        String specialOrgNumber = "!@#$%^&*()";
        String specialCustomerId = "测试中文字符";
        when(authCardHolderFeign.getCustomerBasicInfoByOrgAndCid(specialOrgNumber, specialCustomerId, specialCustomerId))
                .thenReturn(customerBasicResponse);

        // Act
        CustomerBasicInfoDTO result = customerAuthFeignService.findByOrgAndSubCustomerId(specialOrgNumber, specialCustomerId);

        // Assert
        assertThat(result).isEqualTo(customerBasicInfoDTO);
        verify(authCardHolderFeign).getCustomerBasicInfoByOrgAndCid(specialOrgNumber, specialCustomerId, specialCustomerId);
    }

    @Test
    @DisplayName("获取客户授权信息 - 批处理模式测试")
    void getCustomerAuthorizationByCustomerId_BatchMode_CallsFeignDirectly() {
        // Arrange
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(true);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID))
                .thenReturn(customerAuthResponse);

        // Act
        CustomerAuthorizationInfoDTO result = customerAuthFeignService.getCustomerAuthorizationByCustomerId(TEST_ORG_NUMBER, TEST_CUSTOMER_ID);

        // Assert
        assertThat(result).isEqualTo(customerAuthorizationInfoDTO);
        verify(authCardHolderFeign).getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID);
        verify(customerAuthResponse).getData();
    }

    @Test
    @DisplayName("测试Feign服务异常处理")
    void getCustomerAuthorizationByCustomerId_FeignThrowsException_PropagatesException() {
        // Arrange
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID))
                .thenThrow(new RuntimeException("Feign调用异常"));

        // Act & Assert
        assertThatThrownBy(() -> customerAuthFeignService.getCustomerAuthorizationByCustomerId(TEST_ORG_NUMBER, TEST_CUSTOMER_ID))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Feign调用异常");
        verify(authCardHolderFeign).getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID);
    }

    @Test
    @DisplayName("测试客户基础信息Feign服务异常处理")
    void findByOrgAndSubCustomerId_FeignThrowsException_PropagatesException() {
        // Arrange
        when(authCardHolderFeign.getCustomerBasicInfoByOrgAndCid(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER, TEST_SUB_CUSTOMER))
                .thenThrow(new RuntimeException("客户基础信息查询异常"));

        // Act & Assert
        assertThatThrownBy(() -> customerAuthFeignService.findByOrgAndSubCustomerId(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("客户基础信息查询异常");
        verify(authCardHolderFeign).getCustomerBasicInfoByOrgAndCid(TEST_ORG_NUMBER, TEST_SUB_CUSTOMER, TEST_SUB_CUSTOMER);
    }

    @Test
    @DisplayName("测试方法签名和返回类型")
    void testMethodSignatures() throws NoSuchMethodException {
        // 验证getCustomerAuthorizationByCustomerId方法签名
        java.lang.reflect.Method method1 = CustomerAuthFeignService.class
                .getDeclaredMethod("getCustomerAuthorizationByCustomerId", String.class, String.class);
        assertThat(method1.getReturnType()).isEqualTo(CustomerAuthorizationInfoDTO.class);

        // 验证findByOrgAndSubCustomerId方法签名
        java.lang.reflect.Method method2 = CustomerAuthFeignService.class
                .getDeclaredMethod("findByOrgAndSubCustomerId", String.class, String.class);
        assertThat(method2.getReturnType()).isEqualTo(CustomerBasicInfoDTO.class);
    }

    @Test
    @DisplayName("测试类注解和依赖注入")
    void testClassAnnotationsAndDependencyInjection() {
        // 验证依赖注入
        assertThat(customerAuthFeignService).isNotNull();
        assertThat(authCardHolderFeign).isNotNull();

        // 验证类存在
        assertThat(CustomerAuthFeignService.class).isNotNull();
        assertThat(CustomerAuthFeignService.class.getSimpleName()).isEqualTo("CustomerAuthFeignService");
    }

    @Test
    @DisplayName("测试多次调用相同方法")
    void testMultipleCallsSameMethod() {
        // Arrange
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID))
                .thenReturn(customerAuthResponse);

        // Act - 多次调用
        CustomerAuthorizationInfoDTO result1 = customerAuthFeignService.getCustomerAuthorizationByCustomerId(TEST_ORG_NUMBER, TEST_CUSTOMER_ID);
        CustomerAuthorizationInfoDTO result2 = customerAuthFeignService.getCustomerAuthorizationByCustomerId(TEST_ORG_NUMBER, TEST_CUSTOMER_ID);

        // Assert
        assertThat(result1).isEqualTo(customerAuthorizationInfoDTO);
        assertThat(result2).isEqualTo(customerAuthorizationInfoDTO);
        verify(authCardHolderFeign, times(2)).getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID);
    }

    @Test
    @DisplayName("测试CustAccountBO静态方法调用")
    void testCustAccountBOStaticMethodCall() {
        // Arrange - 测试不同的批处理状态
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false, true);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID))
                .thenReturn(customerAuthResponse);

        // Act
        customerAuthFeignService.getCustomerAuthorizationByCustomerId(TEST_ORG_NUMBER, TEST_CUSTOMER_ID);
        customerAuthFeignService.getCustomerAuthorizationByCustomerId(TEST_ORG_NUMBER, TEST_CUSTOMER_ID);

        // Assert - 验证静态方法被调用
        custAccountBOMock.verify(() -> CustAccountBO.isBatch(), times(2));
    }

    @Test
    @DisplayName("测试参数传递的一致性")
    void testParameterPassingConsistency() {
        // Arrange
        custAccountBOMock.when(CustAccountBO::isBatch).thenReturn(false);
        when(authCardHolderFeign.getCustomerAuthInfoOrgAndCid(anyString(), anyString(), anyString()))
                .thenAnswer(invocation -> {
                    // 验证第二个和第三个参数相同
                    String arg2 = invocation.getArgument(1);
                    String arg3 = invocation.getArgument(2);
                    assertThat(arg2).isEqualTo(arg3);
                    return customerAuthResponse;
                });

        // Act
        customerAuthFeignService.getCustomerAuthorizationByCustomerId(TEST_ORG_NUMBER, TEST_CUSTOMER_ID);

        // Assert - 验证参数传递正确
        verify(authCardHolderFeign).getCustomerAuthInfoOrgAndCid(TEST_ORG_NUMBER, TEST_CUSTOMER_ID, TEST_CUSTOMER_ID);
    }
}